{"name": "dify-vue-monorepo", "version": "1.5.1", "private": true, "type": "module", "engines": {"node": ">=20.10.0", "pnpm": ">=9.12.0"}, "packageManager": "pnpm@10.12.4", "scripts": {"dev": "turbo dev", "build": "turbo build", "lint": "turbo lint", "test": "turbo test", "clean": "turbo clean", "dev:admin": "pnpm -F @dify/web-admin dev", "dev:chat": "pnpm -F @dify/web-chat dev", "build:admin": "pnpm -F @dify/web-admin build", "build:chat": "pnpm -F @dify/web-chat build", "type-check": "turbo type-check", "format": "prettier --write .", "prepare": "husky install"}, "devDependencies": {"@changesets/cli": "^2.27.0", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "cross-env": "^7.0.3", "eslint": "^9.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-vue": "^9.0.0", "husky": "^9.0.0", "lint-staged": "^15.0.0", "prettier": "^3.0.0", "turbo": "^2.0.0", "typescript": "^5.0.0", "vitest": "^2.0.0"}, "pnpm": {"overrides": {"vue": "^3.5.0"}}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}}
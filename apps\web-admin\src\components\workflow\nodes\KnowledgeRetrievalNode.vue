<template>
  <BaseNode
    :data="data"
    :selected="selected"
    :disabled="disabled"
    :status="status"
    @click="$emit('click', $event)"
    @double-click="$emit('doubleClick', $event)"
  >
    <template #content>
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <p class="text-xs font-medium text-gray-700">知识检索</p>
          <span class="text-xs text-gray-500 bg-purple-100 px-2 py-1 rounded">
            {{ data.retrieval_mode === 'multiple' ? '多路检索' : '单路检索' }}
          </span>
        </div>
        
        <div v-if="data.dataset_ids?.length" class="text-xs text-gray-600">
          <span class="font-medium">知识库:</span> {{ data.dataset_ids.length }} 个
        </div>
        
        <div v-if="data.multiple_retrieval_config" class="space-y-1">
          <div class="text-xs text-gray-600">
            <span class="font-medium">Top K:</span> {{ data.multiple_retrieval_config.top_k }}
          </div>
          <div class="text-xs text-gray-600">
            <span class="font-medium">相似度阈值:</span> {{ data.multiple_retrieval_config.score_threshold }}
          </div>
        </div>
        
        <div class="flex flex-wrap gap-1">
          <span v-if="data.multiple_retrieval_config?.reranking_model" class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">
            重排序
          </span>
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">
import BaseNode from './BaseNode.vue'
import type { KnowledgeRetrievalNodeData } from '@/types/workflow'

interface Props {
  data: KnowledgeRetrievalNodeData
  selected?: boolean
  disabled?: boolean
  status?: 'running' | 'success' | 'error' | 'idle'
}

interface Emits {
  (e: 'click', event: MouseEvent): void
  (e: 'doubleClick', event: MouseEvent): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>

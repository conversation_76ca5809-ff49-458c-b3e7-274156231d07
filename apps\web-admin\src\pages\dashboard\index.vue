<template>
  <div class="dashboard animate-fade-in">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">仪表板</h1>
      <p class="mt-2 text-base text-gray-600">
        欢迎回来！这里是您的AI应用程序概览。
      </p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
      <div class="card hover:shadow-md transition-shadow duration-200 animate-slide-up">
        <div class="card-body">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="p-3 bg-primary-100 rounded-lg">
                  <RectangleStackIcon class="h-6 w-6 text-primary-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">应用总数</p>
                <p class="text-2xl font-bold text-gray-900">12</p>
                <p class="text-xs text-green-600 mt-1">+2 本月</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card hover:shadow-md transition-shadow duration-200 animate-slide-up" style="animation-delay: 0.1s">
        <div class="card-body">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="p-3 bg-green-100 rounded-lg">
                  <ChatBubbleLeftRightIcon class="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">活跃对话</p>
                <p class="text-2xl font-bold text-gray-900">1,234</p>
                <p class="text-xs text-green-600 mt-1">+15% 今日</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card hover:shadow-md transition-shadow duration-200 animate-slide-up" style="animation-delay: 0.2s">
        <div class="card-body">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="p-3 bg-blue-100 rounded-lg">
                  <BookOpenIcon class="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">知识库</p>
                <p class="text-2xl font-bold text-gray-900">8</p>
                <p class="text-xs text-blue-600 mt-1">3 个已更新</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card hover:shadow-md transition-shadow duration-200 animate-slide-up" style="animation-delay: 0.3s">
        <div class="card-body">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="p-3 bg-purple-100 rounded-lg">
                  <CpuChipIcon class="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">今日API调用</p>
                <p class="text-2xl font-bold text-gray-900">5,678</p>
                <p class="text-xs text-purple-600 mt-1">+8% 较昨日</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6">
      <div class="card animate-slide-up" style="animation-delay: 0.4s">
        <div class="card-header">
          <h3 class="text-lg font-semibold text-gray-900">最近应用</h3>
          <p class="text-sm text-gray-500 mt-1">最近创建或更新的应用</p>
        </div>
        <div class="card-body">
          <div class="space-y-4">
            <RouterLink
              v-for="app in recentApps"
              :key="app.id"
              :to="`/apps/${app.id}`"
              class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
            >
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-10 w-10 rounded-lg bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center">
                    <RectangleStackIcon class="h-5 w-5 text-white" />
                  </div>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-semibold text-gray-900">{{ app.name }}</p>
                  <p class="text-xs text-gray-500">{{ app.type }}</p>
                </div>
              </div>
              <div class="text-right">
                <div class="text-xs text-gray-500">{{ formatDate(app.created_at) }}</div>
                <div class="text-xs text-green-600 font-medium">活跃</div>
              </div>
            </RouterLink>
          </div>
          <div class="mt-4 pt-4 border-t border-gray-200">
            <RouterLink
              to="/apps"
              class="text-sm text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200"
            >
              查看所有应用 →
            </RouterLink>
          </div>
        </div>
      </div>

      <div class="card animate-slide-up" style="animation-delay: 0.5s">
        <div class="card-header">
          <h3 class="text-lg font-semibold text-gray-900">系统状态</h3>
          <p class="text-sm text-gray-500 mt-1">实时系统健康监控</p>
        </div>
        <div class="card-body">
          <div class="space-y-4">
            <div class="flex items-center justify-between p-3 rounded-lg bg-green-50">
              <div class="flex items-center">
                <div class="h-2 w-2 bg-green-500 rounded-full mr-3"></div>
                <span class="text-sm font-medium text-gray-700">API 服务</span>
              </div>
              <span class="badge badge-success">正常运行</span>
            </div>
            <div class="flex items-center justify-between p-3 rounded-lg bg-green-50">
              <div class="flex items-center">
                <div class="h-2 w-2 bg-green-500 rounded-full mr-3"></div>
                <span class="text-sm font-medium text-gray-700">数据库</span>
              </div>
              <span class="badge badge-success">健康</span>
            </div>
            <div class="flex items-center justify-between p-3 rounded-lg bg-green-50">
              <div class="flex items-center">
                <div class="h-2 w-2 bg-green-500 rounded-full mr-3"></div>
                <span class="text-sm font-medium text-gray-700">向量数据库</span>
              </div>
              <span class="badge badge-success">已连接</span>
            </div>
            <div class="flex items-center justify-between p-3 rounded-lg bg-yellow-50">
              <div class="flex items-center">
                <div class="h-2 w-2 bg-yellow-500 rounded-full mr-3"></div>
                <span class="text-sm font-medium text-gray-700">模型服务</span>
              </div>
              <span class="badge badge-warning">性能降级</span>
            </div>
          </div>
          <div class="mt-4 pt-4 border-t border-gray-200">
            <RouterLink
              to="/monitoring"
              class="text-sm text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200"
            >
              查看详细监控 →
            </RouterLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  RectangleStackIcon,
  ChatBubbleLeftRightIcon,
  BookOpenIcon,
  CpuChipIcon
} from '@heroicons/vue/24/outline'

const recentApps = ref([
  {
    id: '1',
    name: '客服机器人',
    type: '聊天机器人',
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name: '内容生成器',
    type: '文本生成',
    created_at: '2024-01-14T15:30:00Z'
  },
  {
    id: '3',
    name: '数据分析助手',
    type: '智能代理',
    created_at: '2024-01-13T09:15:00Z'
  }
])

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString()
}
</script>

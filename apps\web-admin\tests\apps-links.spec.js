import { test, expect } from '@playwright/test';

test.describe('Apps Page Links Test', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到Apps页面
    await page.goto('/apps');
    
    // 等待页面加载完成
    await page.waitForSelector('.apps-page');
  });

  test('应该正确显示中文化的Apps页面', async ({ page }) => {
    // 验证页面标题
    await expect(page.locator('h1')).toContainText('应用程序');
    
    // 验证页面描述
    await expect(page.locator('p')).toContainText('创建和管理您的AI应用程序');
    
    // 验证创建按钮
    await expect(page.locator('text=创建应用')).toBeVisible();
    
    // 验证搜索框
    await expect(page.locator('input[placeholder="搜索应用程序..."]')).toBeVisible();
    
    // 验证类别选择器
    await expect(page.locator('select')).toContainText('所有类别');
  });

  test('应该能够点击应用卡片跳转到详情页', async ({ page }) => {
    // 等待应用卡片加载
    await page.waitForSelector('.card');
    
    // 点击第一个应用卡片
    const firstAppCard = page.locator('.card').first();
    const appLink = firstAppCard.locator('a').first();
    
    await expect(appLink).toBeVisible();
    await appLink.click();
    
    // 验证跳转到应用详情页
    await expect(page.url()).toMatch(/\/apps\/\d+$/);
    
    // 验证详情页内容
    await expect(page.locator('h1')).toContainText('应用详情');
  });

  test('应该能够从详情页跳转到编辑页', async ({ page }) => {
    // 先跳转到应用详情页
    await page.goto('/apps/1');
    
    // 等待页面加载
    await page.waitForSelector('.app-detail');
    
    // 点击编辑按钮
    const editButton = page.locator('text=编辑应用');
    await expect(editButton).toBeVisible();
    await editButton.click();
    
    // 验证跳转到编辑页
    await expect(page.url()).toMatch(/\/apps\/\d+\/edit$/);
    
    // 验证编辑页内容
    await expect(page.locator('h1')).toContainText('编辑应用');
  });

  test('应该能够使用返回链接正确导航', async ({ page }) => {
    // 从应用详情页开始
    await page.goto('/apps/1');
    
    // 点击返回应用列表链接
    const backToListLink = page.locator('text=返回应用列表');
    await expect(backToListLink).toBeVisible();
    await backToListLink.click();
    
    // 验证返回到应用列表页
    await expect(page.url()).toBe('http://localhost:5173/apps');
    await expect(page.locator('h1')).toContainText('应用程序');
  });

  test('应该能够从编辑页返回详情页', async ({ page }) => {
    // 从编辑页开始
    await page.goto('/apps/1/edit');
    
    // 点击返回应用详情链接
    const backToDetailLink = page.locator('text=返回应用详情');
    await expect(backToDetailLink).toBeVisible();
    await backToDetailLink.click();
    
    // 验证返回到应用详情页
    await expect(page.url()).toBe('http://localhost:5173/apps/1');
    await expect(page.locator('h1')).toContainText('应用详情');
  });

  test('应该能够打开创建应用模态框', async ({ page }) => {
    // 点击创建应用按钮
    const createButton = page.locator('text=创建应用').first();
    await createButton.click();
    
    // 验证模态框打开
    await expect(page.locator('text=创建应用程序')).toBeVisible();
    
    // 验证表单字段
    await expect(page.locator('input[placeholder="输入应用名称..."]')).toBeVisible();
    await expect(page.locator('select')).toContainText('选择应用类型');
    await expect(page.locator('textarea[placeholder="简要描述您的应用程序..."]')).toBeVisible();
    
    // 关闭模态框
    await page.locator('text=取消').click();
    await expect(page.locator('text=创建应用程序')).not.toBeVisible();
  });

  test('应该能够使用搜索功能', async ({ page }) => {
    // 在搜索框中输入
    const searchInput = page.locator('input[placeholder="搜索应用程序..."]');
    await searchInput.fill('客服');
    
    // 验证搜索结果
    await expect(page.locator('.card')).toContainText('客服机器人');
    
    // 清空搜索
    await searchInput.clear();
    
    // 验证显示所有应用
    const cardCount = await page.locator('.card').count();
    expect(cardCount).toBeGreaterThan(1);
  });

  test('应该能够使用类别筛选', async ({ page }) => {
    // 选择聊天机器人类别
    const categorySelect = page.locator('select');
    await categorySelect.selectOption('chatbot');
    
    // 验证只显示聊天机器人类型的应用
    const visibleCards = page.locator('.card');
    const cardCount = await visibleCards.count();
    
    // 验证每个可见卡片都是聊天机器人类型
    for (let i = 0; i < cardCount; i++) {
      const card = visibleCards.nth(i);
      await expect(card).toContainText('聊天机器人');
    }
  });

  test('应该能够编辑和删除应用', async ({ page }) => {
    // 测试编辑按钮
    const editButton = page.locator('[title="编辑应用"]').first();
    await expect(editButton).toBeVisible();
    
    // 测试删除按钮
    const deleteButton = page.locator('[title="删除应用"]').first();
    await expect(deleteButton).toBeVisible();
    
    // 点击删除按钮（但取消确认）
    page.on('dialog', dialog => dialog.dismiss());
    await deleteButton.click();
  });

  test('应该在移动端正确显示', async ({ page, isMobile }) => {
    if (isMobile) {
      // 验证响应式布局
      await expect(page.locator('.apps-page')).toBeVisible();
      
      // 验证搜索和筛选在移动端的布局
      const searchContainer = page.locator('.flex-col.sm\\:flex-row');
      await expect(searchContainer).toBeVisible();
      
      // 验证应用卡片在移动端的显示
      const appGrid = page.locator('.grid');
      await expect(appGrid).toBeVisible();
    }
  });

  test('应该处理不存在的应用ID', async ({ page }) => {
    // 访问不存在的应用ID
    await page.goto('/apps/999');
    
    // 应该重定向到应用列表页
    await expect(page.url()).toBe('http://localhost:5173/apps');
  });
});

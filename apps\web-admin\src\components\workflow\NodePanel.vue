<template>
  <div class="node-panel h-full flex flex-col">
    <!-- 面板标题 -->
    <div class="flex-shrink-0 px-4 py-3 border-b border-gray-200">
      <h3 class="text-sm font-medium text-gray-900">节点库</h3>
    </div>
    
    <!-- 搜索框 -->
    <div class="flex-shrink-0 p-4">
      <div class="relative">
        <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索节点..."
          class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>
    </div>
    
    <!-- 节点分类 -->
    <div class="flex-1 overflow-y-auto">
      <div class="px-4 pb-4 space-y-4">
        <div
          v-for="(category, categoryKey) in filteredCategories"
          :key="categoryKey"
          class="space-y-2"
        >
          <!-- 分类标题 -->
          <h4 class="text-xs font-medium text-gray-500 uppercase tracking-wide">
            {{ category.label }}
          </h4>
          
          <!-- 节点列表 -->
          <div class="space-y-1">
            <div
              v-for="nodeType in category.nodes"
              :key="nodeType"
              :draggable="true"
              @dragstart="handleDragStart($event, nodeType)"
              @click="handleNodeClick(nodeType)"
              class="node-item group cursor-pointer p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all duration-200"
            >
              <div class="flex items-center space-x-3">
                <!-- 节点图标 -->
                <div 
                  :class="[
                    'flex-shrink-0',
                    'w-8',
                    'h-8',
                    'rounded',
                    'flex',
                    'items-center',
                    'justify-center',
                    getNodeColorClass(nodeType)
                  ]"
                >
                  <component 
                    :is="getNodeIcon(nodeType)" 
                    class="w-4 h-4 text-white"
                  />
                </div>
                
                <!-- 节点信息 -->
                <div class="flex-1 min-w-0">
                  <h5 class="text-sm font-medium text-gray-900 group-hover:text-blue-600">
                    {{ getNodeConfig(nodeType)?.label }}
                  </h5>
                  <p class="text-xs text-gray-500 truncate">
                    {{ getNodeConfig(nodeType)?.description }}
                  </p>
                </div>
                
                <!-- 拖拽指示器 -->
                <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Bars3Icon class="w-4 h-4 text-gray-400" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 使用提示 -->
    <div class="flex-shrink-0 p-4 border-t border-gray-200 bg-gray-50">
      <div class="text-xs text-gray-500 space-y-1">
        <p>💡 使用提示:</p>
        <p>• 拖拽节点到画布中添加</p>
        <p>• 点击节点快速添加到中心</p>
        <p>• 连接节点创建工作流</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  MagnifyingGlassIcon,
  Bars3Icon,
  PlayIcon,
  StopIcon,
  CpuChipIcon,
  QuestionMarkCircleIcon,
  CodeBracketIcon,
  DocumentTextIcon,
  GlobeAltIcon,
  WrenchScrewdriverIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/vue/24/outline'
import { getNodeTypesByCategory, getNodeConfig } from './nodes'
import type { NodeType } from '@/types/workflow'

interface Emits {
  (e: 'addNode', nodeType: NodeType, position: { x: number; y: number }): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const searchQuery = ref('')

// 节点图标映射
const nodeIconMap = {
  start: PlayIcon,
  end: StopIcon,
  llm: CpuChipIcon,
  'knowledge-retrieval': MagnifyingGlassIcon,
  'question-classifier': QuestionMarkCircleIcon,
  'if-else': QuestionMarkCircleIcon,
  code: CodeBracketIcon,
  template: DocumentTextIcon,
  'http-request': GlobeAltIcon,
  tools: WrenchScrewdriverIcon,
  answer: ChatBubbleLeftRightIcon
}

// 节点颜色类映射
const nodeColorClassMap = {
  start: 'bg-green-500',
  end: 'bg-red-500',
  llm: 'bg-blue-500',
  'knowledge-retrieval': 'bg-purple-500',
  'question-classifier': 'bg-yellow-500',
  'if-else': 'bg-orange-500',
  code: 'bg-gray-700',
  template: 'bg-indigo-500',
  'http-request': 'bg-teal-500',
  tools: 'bg-pink-500',
  answer: 'bg-cyan-500'
}

// 计算属性
const filteredCategories = computed(() => {
  const categories = getNodeTypesByCategory()
  
  if (!searchQuery.value.trim()) {
    return categories
  }
  
  const query = searchQuery.value.toLowerCase()
  const filtered: typeof categories = {}
  
  Object.entries(categories).forEach(([categoryKey, category]) => {
    const filteredNodes = category.nodes.filter(nodeType => {
      const config = getNodeConfig(nodeType as NodeType)
      return (
        config?.label.toLowerCase().includes(query) ||
        config?.description.toLowerCase().includes(query) ||
        nodeType.toLowerCase().includes(query)
      )
    })
    
    if (filteredNodes.length > 0) {
      filtered[categoryKey as keyof typeof categories] = {
        ...category,
        nodes: filteredNodes
      }
    }
  })
  
  return filtered
})

// 辅助函数
const getNodeIcon = (nodeType: NodeType) => {
  return nodeIconMap[nodeType] || CpuChipIcon
}

const getNodeColorClass = (nodeType: NodeType) => {
  return nodeColorClassMap[nodeType] || 'bg-gray-500'
}

// 事件处理
const handleDragStart = (event: DragEvent, nodeType: NodeType) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/vueflow', nodeType)
    event.dataTransfer.effectAllowed = 'move'
  }
}

const handleNodeClick = (nodeType: NodeType) => {
  // 在画布中心添加节点
  const position = { x: 250, y: 250 }
  emit('addNode', nodeType, position)
}
</script>

<style scoped>
.node-panel {
  background-color: #fafafa;
}

.node-item {
  background-color: white;
  user-select: none;
}

.node-item:hover {
  transform: translateY(-1px);
}

.node-item:active {
  transform: translateY(0);
}
</style>

/**
 * 工作流相关类型定义
 */

// 节点位置
export interface NodePosition {
  x: number
  y: number
}

// 节点数据基础接口
export interface BaseNodeData {
  id: string
  title: string
  desc?: string
  selected?: boolean
}

// 节点类型枚举
export enum NodeType {
  START = 'start',
  END = 'end',
  LLM = 'llm',
  KNOWLEDGE_RETRIEVAL = 'knowledge-retrieval',
  QUESTION_CLASSIFIER = 'question-classifier',
  IF_ELSE = 'if-else',
  CODE = 'code',
  TEMPLATE = 'template',
  HTTP_REQUEST = 'http-request',
  TOOLS = 'tools',
  ANSWER = 'answer'
}

// Start 节点数据
export interface StartNodeData extends BaseNodeData {
  type: NodeType.START
  variables: Array<{
    variable: string
    type: 'text' | 'paragraph' | 'select' | 'number' | 'file' | 'file-list'
    label: string
    required: boolean
    options?: string[]
    max_length?: number
  }>
}

// End 节点数据
export interface EndNodeData extends BaseNodeData {
  type: NodeType.END
  outputs: Array<{
    variable: string
    type: string
    value: string
  }>
}

// LLM 节点数据
export interface LLMNodeData extends BaseNodeData {
  type: NodeType.LLM
  model: {
    provider: string
    name: string
    mode: string
    completion_params: Record<string, any>
  }
  prompt_template: Array<{
    role: 'system' | 'user' | 'assistant'
    text: string
  }>
  memory?: {
    role_prefix?: {
      user: string
      assistant: string
    }
    window: {
      enabled: boolean
      size: number
    }
  }
  context?: {
    enabled: boolean
    variable_selector: string[]
  }
  vision?: {
    enabled: boolean
    configs?: {
      detail: 'low' | 'high'
    }
  }
}

// Knowledge Retrieval 节点数据
export interface KnowledgeRetrievalNodeData extends BaseNodeData {
  type: NodeType.KNOWLEDGE_RETRIEVAL
  dataset_ids: string[]
  query_variable_selector: string[]
  retrieval_mode: 'single' | 'multiple'
  multiple_retrieval_config?: {
    top_k: number
    score_threshold: number
    reranking_model?: {
      provider: string
      model: string
    }
  }
}

// Question Classifier 节点数据
export interface QuestionClassifierNodeData extends BaseNodeData {
  type: NodeType.QUESTION_CLASSIFIER
  query_variable_selector: string[]
  classes: Array<{
    id: string
    name: string
    description: string
  }>
  model: {
    provider: string
    name: string
    completion_params: Record<string, any>
  }
}

// IF/ELSE 节点数据
export interface IfElseNodeData extends BaseNodeData {
  type: NodeType.IF_ELSE
  conditions: Array<{
    id: string
    variable_selector: string[]
    comparison_operator: 'is' | 'is not' | 'contains' | 'not contains' | 'start with' | 'end with' | 'is empty' | 'is not empty'
    value: string
  }>
  logical_operator: 'and' | 'or'
}

// Code 节点数据
export interface CodeNodeData extends BaseNodeData {
  type: NodeType.CODE
  code_language: 'python3' | 'javascript'
  code: string
  variables: Array<{
    variable: string
    type: string
  }>
  outputs: Array<{
    variable: string
    type: string
  }>
}

// Template 节点数据
export interface TemplateNodeData extends BaseNodeData {
  type: NodeType.TEMPLATE
  template: string
  variables: Array<{
    variable: string
    value_selector: string[]
  }>
}

// HTTP Request 节点数据
export interface HttpRequestNodeData extends BaseNodeData {
  type: NodeType.HTTP_REQUEST
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  url: string
  headers: Record<string, string>
  params?: Record<string, string>
  body?: {
    type: 'none' | 'form-data' | 'x-www-form-urlencoded' | 'raw-text' | 'json'
    data: string | Record<string, any>
  }
  timeout: number
}

// Tools 节点数据
export interface ToolsNodeData extends BaseNodeData {
  type: NodeType.TOOLS
  provider_id: string
  provider_type: string
  provider_name: string
  tool_name: string
  tool_parameters: Record<string, any>
}

// Answer 节点数据
export interface AnswerNodeData extends BaseNodeData {
  type: NodeType.ANSWER
  answer: string
  variables: Array<{
    variable: string
    value_selector: string[]
  }>
}

// 联合节点数据类型
export type NodeData = 
  | StartNodeData
  | EndNodeData
  | LLMNodeData
  | KnowledgeRetrievalNodeData
  | QuestionClassifierNodeData
  | IfElseNodeData
  | CodeNodeData
  | TemplateNodeData
  | HttpRequestNodeData
  | ToolsNodeData
  | AnswerNodeData

// 工作流节点
export interface WorkflowNode {
  id: string
  type: NodeType
  position: NodePosition
  data: NodeData
  width?: number
  height?: number
}

// 工作流边
export interface WorkflowEdge {
  id: string
  source: string
  target: string
  sourceHandle?: string
  targetHandle?: string
  type?: string
  data?: Record<string, any>
}

// 工作流变量
export interface WorkflowVariable {
  id: string
  name: string
  type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'file'
  value?: any
  description?: string
}

// 工作流配置
export interface WorkflowConfig {
  id: string
  name: string
  description?: string
  icon?: string
  icon_background?: string
  mode: 'workflow' | 'chatflow'
  graph: {
    nodes: WorkflowNode[]
    edges: WorkflowEdge[]
    viewport: {
      x: number
      y: number
      zoom: number
    }
  }
  features?: {
    file_upload?: {
      image: {
        enabled: boolean
        number_limits: number
        detail: 'low' | 'high'
        transfer_methods: string[]
      }
    }
    opening_statement?: string
    suggested_questions?: string[]
    speech_to_text?: {
      enabled: boolean
    }
    text_to_speech?: {
      enabled: boolean
      voice?: string
    }
    retriever_resource?: {
      enabled: boolean
    }
    annotation_reply?: {
      enabled: boolean
    }
  }
  variables: WorkflowVariable[]
  created_by: string
  created_at: string
  updated_by?: string
  updated_at?: string
}

// 工作流运行状态
export enum WorkflowRunStatus {
  RUNNING = 'running',
  SUCCEEDED = 'succeeded',
  FAILED = 'failed',
  STOPPED = 'stopped'
}

// 工作流运行记录
export interface WorkflowRun {
  id: string
  workflow_id: string
  status: WorkflowRunStatus
  inputs: Record<string, any>
  outputs?: Record<string, any>
  error?: string
  elapsed_time: number
  total_tokens?: number
  total_steps: number
  created_by: string
  created_at: string
  finished_at?: string
}

// 节点运行状态
export interface NodeRunStatus {
  node_id: string
  status: 'running' | 'succeeded' | 'failed'
  inputs?: Record<string, any>
  outputs?: Record<string, any>
  error?: string
  elapsed_time?: number
  execution_metadata?: Record<string, any>
}

// 工作流运行详情
export interface WorkflowRunDetail extends WorkflowRun {
  node_execution_infos: NodeRunStatus[]
}

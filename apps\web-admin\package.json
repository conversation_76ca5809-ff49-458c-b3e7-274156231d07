{"name": "@dify/web-admin", "version": "1.5.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "type-check": "vue-tsc --noEmit", "test": "vitest"}, "dependencies": {"vue": "^3.5.0", "vue-router": "^4.4.0", "pinia": "^2.2.0", "@tanstack/vue-query": "^5.0.0", "@vueuse/core": "^11.0.0", "@headlessui/vue": "^1.7.0", "@heroicons/vue": "^2.0.0", "tailwindcss": "^3.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "tailwind-merge": "^2.0.0", "dayjs": "^1.11.0", "ky": "^1.7.0", "@vue-flow/core": "^1.0.0", "@vue-flow/controls": "^1.0.0", "@vue-flow/minimap": "^1.0.0", "@vue-flow/background": "^1.0.0", "monaco-editor": "^0.45.0", "@monaco-editor/loader": "^1.4.0", "vue-i18n": "^9.8.0", "lodash-es": "^4.17.21", "uuid": "^10.0.0", "mitt": "^3.0.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "@vitejs/plugin-vue-jsx": "^4.0.0", "vite": "^5.0.0", "vue-tsc": "^2.0.0", "typescript": "^5.0.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@types/lodash-es": "^4.17.0", "@types/uuid": "^10.0.0", "unplugin-auto-import": "^0.17.0", "unplugin-vue-components": "^0.26.0"}}
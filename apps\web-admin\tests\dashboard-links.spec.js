import { test, expect } from '@playwright/test';

test.describe('Dashboard Links Test', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到Dashboard页面
    await page.goto('/dashboard');

    // 等待页面加载完成
    await page.waitForSelector('.dashboard');
  });

  test('应该能够点击"查看所有应用"链接', async ({ page }) => {
    // 查找并点击"查看所有应用"链接
    const appsLink = page.locator('text=查看所有应用 →');
    await expect(appsLink).toBeVisible();

    // 点击链接
    await appsLink.click();

    // 验证是否跳转到应用页面
    await expect(page).toHaveURL('/apps');

    // 验证页面内容
    await expect(page.locator('h1')).toContainText('应用');
  });

  test('应该能够点击"查看详细监控"链接', async ({ page }) => {
    // 查找并点击"查看详细监控"链接
    const monitoringLink = page.locator('text=查看详细监控 →');
    await expect(monitoringLink).toBeVisible();

    // 点击链接
    await monitoringLink.click();

    // 验证是否跳转到监控页面
    await expect(page).toHaveURL('/monitoring');

    // 验证页面内容
    await expect(page.locator('h1')).toContainText('监控');
  });

  test('应该能够点击最近应用列表中的应用', async ({ page }) => {
    // 查找第一个应用项目
    const firstApp = page.locator('.space-y-4 a').first();
    await expect(firstApp).toBeVisible();

    // 获取应用名称
    const appName = await firstApp.locator('p.text-sm.font-semibold').textContent();

    // 点击应用
    await firstApp.click();

    // 验证是否跳转到应用详情页面
    await expect(page.url()).toMatch(/\/apps\/\d+/);

    // 验证页面内容
    await expect(page.locator('h1')).toContainText('Application Detail');
  });

  test('应该验证所有链接都有正确的hover效果', async ({ page }) => {
    // 测试"查看所有应用"链接的hover效果
    const appsLink = page.locator('text=查看所有应用 →');
    await appsLink.hover();

    // 验证hover样式
    const appsLinkColor = await appsLink.evaluate(el =>
      window.getComputedStyle(el).color
    );

    // 测试"查看详细监控"链接的hover效果
    const monitoringLink = page.locator('text=查看详细监控 →');
    await monitoringLink.hover();

    // 验证hover样式
    const monitoringLinkColor = await monitoringLink.evaluate(el =>
      window.getComputedStyle(el).color
    );

    // 测试应用项目的hover效果
    const firstApp = page.locator('.space-y-4 a').first();
    await firstApp.hover();

    // 验证背景色变化
    const appBgColor = await firstApp.evaluate(el =>
      window.getComputedStyle(el).backgroundColor
    );
  });

  test('应该验证链接的可访问性', async ({ page }) => {
    // 检查所有链接是否有正确的角色和属性
    const links = page.locator('a');
    const linkCount = await links.count();

    for (let i = 0; i < linkCount; i++) {
      const link = links.nth(i);

      // 验证链接是否可见
      if (await link.isVisible()) {
        // 验证链接有href属性
        const href = await link.getAttribute('href');
        expect(href).toBeTruthy();

        // 验证链接可以被键盘访问
        await link.focus();
        const isFocused = await link.evaluate(el => el === document.activeElement);
        expect(isFocused).toBe(true);
      }
    }
  });

  test('应该在移动端正确显示链接', async ({ page, isMobile }) => {
    if (isMobile) {
      // 验证移动端链接布局
      const appsLink = page.locator('text=查看所有应用 →');
      await expect(appsLink).toBeVisible();

      const monitoringLink = page.locator('text=查看详细监控 →');
      await expect(monitoringLink).toBeVisible();

      // 验证应用列表在移动端的显示
      const appsList = page.locator('.space-y-4');
      await expect(appsList).toBeVisible();
    }
  });

  test('应该处理链接点击的错误情况', async ({ page }) => {
    // 监听控制台错误
    const consoleErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // 点击所有链接
    const appsLink = page.locator('text=查看所有应用 →');
    await appsLink.click();
    await page.goBack();

    const monitoringLink = page.locator('text=查看详细监控 →');
    await monitoringLink.click();
    await page.goBack();

    // 验证没有控制台错误
    expect(consoleErrors).toHaveLength(0);
  });
});

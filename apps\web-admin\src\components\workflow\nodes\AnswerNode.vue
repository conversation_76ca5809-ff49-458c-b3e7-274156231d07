<template>
  <BaseNode
    :data="data"
    :selected="selected"
    :disabled="disabled"
    :status="status"
    @click="$emit('click', $event)"
    @double-click="$emit('doubleClick', $event)"
  >
    <template #content>
      <div class="space-y-2">
        <p class="text-xs font-medium text-gray-700">回答节点</p>
        
        <div v-if="data.answer" class="bg-gray-50 p-2 rounded text-xs">
          <p class="text-gray-600">
            {{ data.answer.substring(0, 100) }}{{ data.answer.length > 100 ? '...' : '' }}
          </p>
        </div>
        
        <div v-if="data.variables?.length" class="space-y-1">
          <p class="text-xs font-medium text-gray-700">使用变量:</p>
          <div class="space-y-1">
            <div 
              v-for="variable in data.variables.slice(0, 3)" 
              :key="variable.variable"
              class="flex items-center text-xs text-gray-600"
            >
              <span class="w-2 h-2 bg-cyan-400 rounded-full mr-2"></span>
              <span class="truncate">{{ variable.variable }}</span>
            </div>
            <div v-if="data.variables.length > 3" class="text-xs text-gray-400">
              +{{ data.variables.length - 3 }} 更多...
            </div>
          </div>
        </div>
        
        <div v-else class="text-xs text-gray-400">
          暂无配置内容
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">
import BaseNode from './BaseNode.vue'
import type { AnswerNodeData } from '@/types/workflow'

interface Props {
  data: AnswerNodeData
  selected?: boolean
  disabled?: boolean
  status?: 'running' | 'success' | 'error' | 'idle'
}

interface Emits {
  (e: 'click', event: MouseEvent): void
  (e: 'doubleClick', event: MouseEvent): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>

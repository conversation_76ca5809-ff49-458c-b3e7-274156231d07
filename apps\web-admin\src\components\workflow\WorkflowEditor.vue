<template>
  <div class="workflow-editor h-full flex flex-col bg-gray-50">
    <!-- 工具栏 -->
    <div class="flex-shrink-0 bg-white border-b border-gray-200 px-4 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <h2 class="text-lg font-semibold text-gray-900">
            {{ workflowName || '工作流编辑器' }}
          </h2>
          <div class="flex items-center space-x-2">
            <button
              @click="handleSave"
              :disabled="!canSave"
              class="btn btn-primary btn-sm"
            >
              <DocumentArrowUpIcon class="w-4 h-4 mr-1" />
              保存
            </button>
            <button
              @click="handleExecute"
              :disabled="!canExecute"
              class="btn btn-success btn-sm"
            >
              <PlayIcon class="w-4 h-4 mr-1" />
              {{ isExecuting ? '执行中...' : '运行' }}
            </button>
            <button
              v-if="isExecuting"
              @click="handleStop"
              class="btn btn-danger btn-sm"
            >
              <StopIcon class="w-4 h-4 mr-1" />
              停止
            </button>
          </div>
        </div>

        <div class="flex items-center space-x-2">
          <!-- 缩放控制 -->
          <div class="flex items-center space-x-1 bg-gray-100 rounded px-2 py-1">
            <button
              @click="zoomOut"
              class="p-1 hover:bg-gray-200 rounded"
            >
              <MinusIcon class="w-4 h-4" />
            </button>
            <span class="text-sm text-gray-600 min-w-[3rem] text-center">
              {{ Math.round(viewport.zoom * 100) }}%
            </span>
            <button
              @click="zoomIn"
              class="p-1 hover:bg-gray-200 rounded"
            >
              <PlusIcon class="w-4 h-4" />
            </button>
          </div>

          <!-- 适应画布 -->
          <button
            @click="handleFitView"
            class="p-2 hover:bg-gray-100 rounded"
            title="适应画布"
          >
            <ArrowsPointingOutIcon class="w-4 h-4" />
          </button>

          <!-- 调试按钮 -->
          <button
            @click="debugNodes"
            class="px-3 py-1 text-xs bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200"
            title="调试节点"
          >
            调试
          </button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex">
      <!-- 节点面板 -->
      <div class="flex-shrink-0 w-64 bg-white border-r border-gray-200">
        <NodePanel @add-node="handleAddNode" />
      </div>

      <!-- 画布区域 -->
      <div
        :class="[
          'flex-1 relative transition-all duration-200',
          {
            'bg-blue-50 border-2 border-dashed border-blue-300': isDragOver
          }
        ]"
        @drop="onDrop"
        @dragover="onDragOver"
        @dragenter="onDragEnter"
        @dragleave="onDragLeave"
      >
        <VueFlow
          v-model:nodes="nodes"
          v-model:edges="edges"
          :node-types="nodeTypes"
          :edge-types="edgeTypes"
          :default-edge-options="defaultEdgeOptions"
          :connection-line-options="connectionLineOptions"
          :fit-view-on-init="true"
          :min-zoom="0.1"
          :max-zoom="2"
          :default-viewport="{ x: 0, y: 0, zoom: 1 }"
          @nodes-change="onNodesChange"
          @edges-change="onEdgesChange"
          @connect="onConnect"
          class="workflow-canvas"
        >
          <!-- 背景 -->
          <Background
            pattern-color="#e5e7eb"
            :gap="20"
            variant="dots"
          />

          <!-- 控制面板 -->
          <Controls
            :show-zoom="false"
            :show-fit-view="false"
            :show-interactive="false"
          />

          <!-- 小地图 -->
          <MiniMap
            :node-color="getNodeColor"
            :mask-color="'rgba(0, 0, 0, 0.1)'"
            :position="'bottom-right'"
            class="workflow-minimap"
          />

          <!-- 调试信息 -->
          <template v-if="nodes.length === 0">
            <div class="absolute top-4 left-4 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-2 rounded z-50">
              画布为空 - 节点数量: {{ nodes.length }}
            </div>
          </template>
          <template v-else>
            <div class="absolute top-4 left-4 bg-green-100 border border-green-400 text-green-700 px-4 py-2 rounded z-50">
              节点数量: {{ nodes.length }}
            </div>
          </template>
        </VueFlow>

        <!-- 执行状态覆盖层 -->
        <div
          v-if="isExecuting"
          class="absolute inset-0 bg-black bg-opacity-10 flex items-center justify-center z-50"
        >
          <div class="bg-white rounded-lg shadow-lg p-6 max-w-md">
            <div class="flex items-center space-x-3">
              <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <div>
                <h3 class="text-lg font-medium text-gray-900">工作流执行中</h3>
                <p class="text-sm text-gray-500">请稍候...</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 属性面板 -->
      <div
        v-if="selectedNode"
        class="flex-shrink-0 w-80 bg-white border-l border-gray-200"
      >
        <NodePropertiesPanel
          :node="selectedNode"
          @update="handleNodeUpdate"
          @close="handleCloseProperties"
        />
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="flex-shrink-0 bg-white border-t border-gray-200 px-4 py-2">
      <div class="flex items-center justify-between text-sm text-gray-600">
        <div class="flex items-center space-x-4">
          <span>节点: {{ nodes.length }}</span>
          <span>连接: {{ edges.length }}</span>
          <span v-if="selectedNodes.length > 0">
            已选择: {{ selectedNodes.length }} 个节点
          </span>
        </div>
        <div v-if="lastSaved" class="text-xs text-gray-500">
          最后保存: {{ formatTime(lastSaved) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { VueFlow, useVueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'
import {
  DocumentArrowUpIcon,
  PlayIcon,
  StopIcon,
  MinusIcon,
  PlusIcon,
  ArrowsPointingOutIcon
} from '@heroicons/vue/24/outline'
import { useWorkflowStore } from '@/stores/workflow'
import { nodeComponents, getNodeComponent } from './nodes'
import NodePanel from './NodePanel.vue'
import NodePropertiesPanel from './NodePropertiesPanel.vue'
import type { WorkflowNode, WorkflowEdge, NodeType } from '@/types/workflow'

interface Props {
  workflowId?: string
  workflowName?: string
}

const props = defineProps<Props>()

// 状态管理
const workflowStore = useWorkflowStore()
const { fitView, zoomIn, zoomOut } = useVueFlow()

// 响应式数据 - 从store获取
const nodes = computed({
  get: () => workflowStore.nodes,
  set: (value) => {
    // 通过 store 更新节点
    console.log('设置节点:', value)
  }
})

const edges = computed({
  get: () => workflowStore.edges,
  set: (value) => {
    // 通过 store 更新边
    console.log('设置边:', value)
  }
})

const viewport = computed({
  get: () => workflowStore.viewport,
  set: (value) => workflowStore.updateViewport(value)
})

const selectedNodes = computed(() => workflowStore.selectedNodes)
const selectedEdges = computed(() => workflowStore.selectedEdges)
const isExecuting = computed(() => workflowStore.isExecuting)

// 本地状态
const lastSaved = ref<Date | null>(null)
const selectedNode = ref<WorkflowNode | null>(null)
const isDragOver = ref(false)

// 计算属性
const canSave = computed(() => nodes.value.length > 0)
const canExecute = computed(() => workflowStore.canExecute)

// 节点类型映射
const nodeTypes = computed(() => {
  const types: Record<string, any> = {
    // 添加默认节点类型以防万一
    default: nodeComponents.start
  }

  // 注册所有自定义节点类型
  Object.entries(nodeComponents).forEach(([type, component]) => {
    types[type] = component
  })

  console.log('节点类型映射:', types)
  console.log('可用的节点组件:', Object.keys(nodeComponents))
  console.log('nodeComponents内容:', nodeComponents)
  return types
})

// 边类型配置
const edgeTypes = {}

// 默认边选项
const defaultEdgeOptions = {
  type: 'smoothstep',
  markerEnd: {
    type: 'arrowclosed',
    width: 20,
    height: 20,
    color: '#6b7280'
  }
}

// 连接线选项
const connectionLineOptions = {
  type: 'smoothstep'
}

// 事件处理
const onNodesChange = (changes: any[]) => {
  // 处理节点变化
  console.log('Nodes changed:', changes)
  console.log('当前nodes数量:', nodes.value.length)
}

const onEdgesChange = (changes: any[]) => {
  // 处理边变化
  console.log('Edges changed:', changes)
}

const onConnect = (connection: any) => {
  console.log('节点连接:', connection)

  // 验证连接的有效性
  if (!connection.source || !connection.target) {
    console.warn('无效的连接:', connection)
    return
  }

  // 防止自连接
  if (connection.source === connection.target) {
    console.warn('不能连接到自己')
    return
  }

  // 处理连接
  const edge: WorkflowEdge = {
    id: `edge-${Date.now()}`,
    source: connection.source,
    target: connection.target,
    sourceHandle: connection.sourceHandle,
    targetHandle: connection.targetHandle
  }

  workflowStore.addEdge(edge)
  console.log('添加边:', edge)
}

const onNodeClick = (event: any) => {
  const nodeId = event.node.id
  workflowStore.selectNode(nodeId, event.event.ctrlKey || event.event.metaKey)
  selectedNode.value = event.node
}

const onNodeDoubleClick = (event: any) => {
  // 双击节点打开属性面板
  selectedNode.value = event.node
}

const onEdgeClick = (event: any) => {
  const edgeId = event.edge.id
  workflowStore.selectEdge(edgeId, event.event.ctrlKey || event.event.metaKey)
}

const onPaneClick = () => {
  workflowStore.clearSelection()
  selectedNode.value = null
}

// 拖拽事件处理
const onDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'move'
}

const onDragEnter = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const onDragLeave = (event: DragEvent) => {
  event.preventDefault()
  // 只有当离开整个拖拽区域时才设置为false
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  if (
    event.clientX < rect.left ||
    event.clientX > rect.right ||
    event.clientY < rect.top ||
    event.clientY > rect.bottom
  ) {
    isDragOver.value = false
  }
}

const onDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false

  const nodeType = event.dataTransfer?.getData('application/vueflow') as NodeType
  if (!nodeType) {
    console.warn('无效的拖拽数据')
    return
  }

  // 获取画布容器的位置信息
  const canvasElement = event.currentTarget as HTMLElement
  const rect = canvasElement.getBoundingClientRect()

  // 计算相对于画布的位置
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top

  // 考虑当前视图的缩放和平移
  const position = {
    x: (x - viewport.value.x) / viewport.value.zoom,
    y: (y - viewport.value.y) / viewport.value.zoom
  }

  console.log('拖拽添加节点:', nodeType, '位置:', position)
  handleAddNode(nodeType, position)

  // 添加调试信息
  console.log('当前节点数量:', nodes.value.length)
  console.log('所有节点:', nodes.value)
}

// 工具栏操作
const handleSave = async () => {
  try {
    await workflowStore.saveWorkflowGraph()
    lastSaved.value = new Date()
  } catch (error) {
    console.error('保存失败:', error)
  }
}

const handleExecute = async () => {
  try {
    await workflowStore.executeWorkflow()
  } catch (error) {
    console.error('执行失败:', error)
  }
}

const handleStop = async () => {
  try {
    await workflowStore.stopExecution()
  } catch (error) {
    console.error('停止失败:', error)
  }
}

// 节点操作
const handleAddNode = (nodeType: NodeType, position: { x: number; y: number }) => {
  const nodeId = `node-${Date.now()}`

  // 根据节点类型创建对应的数据结构
  let nodeData: any = {
    id: nodeId,
    type: nodeType,
    title: `新${nodeType}节点`,
    desc: '请配置节点参数'
  }

  // 为不同节点类型添加特定的默认数据
  switch (nodeType) {
    case 'start':
      nodeData = {
        ...nodeData,
        variables: []
      }
      break
    case 'llm':
      nodeData = {
        ...nodeData,
        model: {
          provider: '',
          name: '',
          mode: 'chat',
          completion_params: {}
        },
        prompt_template: [],
        memory: {
          window: { enabled: false, size: 10 }
        },
        context: { enabled: false },
        vision: { enabled: false }
      }
      break
    case 'knowledge-retrieval':
      nodeData = {
        ...nodeData,
        dataset_ids: [],
        query_variable_selector: [],
        retrieval_mode: 'single'
      }
      break
    case 'answer':
      nodeData = {
        ...nodeData,
        answer: '',
        variables: []
      }
      break
    case 'end':
      nodeData = {
        ...nodeData,
        outputs: []
      }
      break
  }

  const newNode: WorkflowNode = {
    id: nodeId,
    type: nodeType,
    position,
    data: nodeData
  }

  console.log('创建新节点:', newNode)
  workflowStore.addNode(newNode)
  console.log('添加节点后，store中的节点数量:', workflowStore.nodes.length)
  console.log('添加节点后，computed nodes数量:', nodes.value.length)
  console.log('添加节点后，computed nodes内容:', nodes.value)
}

const handleNodeUpdate = (nodeId: string, data: any) => {
  workflowStore.updateNode(nodeId, data)
}

const handleCloseProperties = () => {
  selectedNode.value = null
}

// 调试和视图控制方法
const handleFitView = () => {
  console.log('手动调用fitView，当前节点数量:', nodes.value.length)
  console.log('当前节点:', nodes.value)
  fitView({ padding: 0.2, duration: 800 })
}

const debugNodes = () => {
  console.log('=== 节点调试信息 ===')
  console.log('Store中的节点数量:', workflowStore.nodes.length)
  console.log('Store中的节点:', workflowStore.nodes)
  console.log('Computed nodes数量:', nodes.value.length)
  console.log('Computed nodes:', nodes.value)
  console.log('Store中的边数量:', workflowStore.edges.length)
  console.log('Store中的边:', workflowStore.edges)
  console.log('Computed edges数量:', edges.value.length)
  console.log('Computed edges:', edges.value)
  console.log('当前视口:', viewport.value)
  console.log('节点类型映射:', nodeTypes.value)
  console.log('nodeComponents:', nodeComponents)

  // 检查每个节点的类型是否在nodeTypes中
  nodes.value.forEach(node => {
    console.log(`节点 ${node.id} 类型 ${node.type}:`, nodeTypes.value[node.type] ? '✓ 找到组件' : '✗ 缺少组件')
  })
  console.log('===================')
}

// 辅助函数
const getNodeColor = (node: any) => {
  const colorMap: Record<string, string> = {
    start: '#10b981',
    end: '#ef4444',
    llm: '#3b82f6',
    'knowledge-retrieval': '#8b5cf6',
    'question-classifier': '#f59e0b',
    'if-else': '#f97316',
    code: '#6b7280',
    template: '#6366f1',
    'http-request': '#14b8a6',
    tools: '#ec4899',
    answer: '#06b6d4'
  }
  return colorMap[node.type] || '#6b7280'
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 生命周期
onMounted(async () => {
  if (props.workflowId) {
    await workflowStore.loadWorkflow(props.workflowId)
  } else {
    // 如果没有工作流ID，初始化空的编辑器
    workflowStore.reset()
    console.log('初始化空编辑器')

    // 添加测试节点来验证显示
    setTimeout(() => {
      console.log('准备添加测试节点')

      // 添加开始节点
      handleAddNode('start', { x: 100, y: 100 })

      // 添加LLM节点
      setTimeout(() => {
        handleAddNode('llm', { x: 300, y: 100 })

        // 添加结束节点
        setTimeout(() => {
          handleAddNode('end', { x: 500, y: 100 })

          // 添加连接线
          setTimeout(() => {
            const startNode = workflowStore.nodes.find(n => n.type === 'start')
            const llmNode = workflowStore.nodes.find(n => n.type === 'llm')
            const endNode = workflowStore.nodes.find(n => n.type === 'end')

            if (startNode && llmNode) {
              workflowStore.addEdge({
                id: `${startNode.id}-${llmNode.id}`,
                source: startNode.id,
                target: llmNode.id
              })
            }

            if (llmNode && endNode) {
              workflowStore.addEdge({
                id: `${llmNode.id}-${endNode.id}`,
                source: llmNode.id,
                target: endNode.id
              })
            }

            // 最后调用fitView来确保节点可见
            setTimeout(() => {
              console.log('调用fitView，当前节点数量:', nodes.value.length)
              fitView({ padding: 0.2 })
            }, 100)
          }, 100)
        }, 100)
      }, 100)
    }, 1000)
  }
  document.addEventListener('keydown', handleKeyDown)

  // 添加调试信息
  console.log('编辑器挂载完成，当前节点数量:', nodes.value.length)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})

// 键盘快捷键
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 's':
        event.preventDefault()
        handleSave()
        break
      case 'Delete':
      case 'Backspace':
        // 删除选中的节点和边
        selectedNodes.value.forEach(nodeId => {
          workflowStore.deleteNode(nodeId)
        })
        selectedEdges.value.forEach(edgeId => {
          workflowStore.deleteEdge(edgeId)
        })
        break
    }
  }
}
</script>

<style scoped>
.workflow-editor {
  font-family: 'Inter', sans-serif;
}

.workflow-canvas {
  background-color: #f9fafb;
  width: 100%;
  height: 100%;
}

/* 确保Vue Flow节点可见 */
:deep(.vue-flow__node-default) {
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 10px;
  font-size: 12px;
  color: #333;
  min-width: 100px;
  min-height: 40px;
}

.workflow-minimap {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
}

:deep(.vue-flow__node) {
  font-size: 12px;
  transition: none;
}

:deep(.vue-flow__node.selected) {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

:deep(.vue-flow__edge-path) {
  stroke: #6b7280;
  stroke-width: 2;
  transition: stroke 0.2s ease;
}

:deep(.vue-flow__edge.selected .vue-flow__edge-path) {
  stroke: #3b82f6;
  stroke-width: 3;
}

:deep(.vue-flow__connection-line) {
  stroke: #3b82f6;
  stroke-width: 2;
  stroke-dasharray: 5, 5;
}

:deep(.vue-flow__handle) {
  width: 16px !important;
  height: 16px !important;
  border: 2px solid white !important;
  border-radius: 50% !important;
  background: #6b7280 !important;
  cursor: crosshair !important;
  transition: background-color 0.2s ease, transform 0.1s ease !important;
  pointer-events: auto !important;
  position: absolute !important;
  z-index: 1000 !important;
}

:deep(.vue-flow__handle:hover) {
  transform: scale(1.1) !important;
  background: #3b82f6 !important;
}

:deep(.vue-flow__handle-connecting) {
  background: #f59e0b !important;
  transform: scale(1.2) !important;
  box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.3) !important;
}

:deep(.vue-flow__handle-valid) {
  background: #10b981 !important;
  transform: scale(1.15) !important;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3) !important;
}

/* 防止选择文本导致的跳动 */
:deep(.vue-flow__pane) {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 防止连接时画布移动 */
:deep(.vue-flow__pane.connecting) {
  cursor: crosshair !important;
}

:deep(.vue-flow__container) {
  position: relative;
  overflow: hidden;
}

/* 优化拖拽体验 */
:deep(.vue-flow__node.dragging) {
  opacity: 0.8;
  z-index: 1000;
}

/* 稳定的连接线样式 */
:deep(.vue-flow__connection-line-path) {
  stroke: #3b82f6;
  stroke-width: 2;
  stroke-dasharray: 5, 5;
  fill: none;
}

/* 确保Handle在连接时保持稳定 */
:deep(.vue-flow__handle.connecting) {
  pointer-events: auto !important;
  z-index: 1001 !important;
}

/* 防止节点在连接时移动 */
:deep(.vue-flow__node.connecting) {
  pointer-events: none;
}

:deep(.vue-flow__node.connecting .vue-flow__handle) {
  pointer-events: auto !important;
}
</style>

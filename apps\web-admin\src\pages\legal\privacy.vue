<template>
  <div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <button 
          @click="goBack"
          class="inline-flex items-center text-indigo-600 hover:text-indigo-500 transition-colors"
        >
          <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          返回
        </button>
      </div>

      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">隐私政策</h1>
        <p class="text-lg text-gray-600">最后更新：2024年12月19日</p>
      </div>

      <!-- 内容区域 -->
      <div class="bg-white rounded-xl shadow-lg p-8 lg:p-12">
        <div class="prose prose-lg max-w-none">
          <h2>1. 信息收集</h2>
          <p>
            我们可能收集以下类型的信息：
          </p>
          <ul>
            <li><strong>账户信息</strong>：姓名、邮箱地址、密码等注册信息</li>
            <li><strong>使用数据</strong>：应用使用情况、API调用记录、性能数据</li>
            <li><strong>设备信息</strong>：IP地址、浏览器类型、操作系统</li>
            <li><strong>内容数据</strong>：您创建的应用、工作流、知识库内容</li>
          </ul>

          <h2>2. 信息使用</h2>
          <p>我们使用收集的信息用于：</p>
          <ul>
            <li>提供和改进我们的服务</li>
            <li>处理您的请求和交易</li>
            <li>发送服务相关通知</li>
            <li>进行安全监控和欺诈防护</li>
            <li>分析服务使用情况以优化用户体验</li>
          </ul>

          <h2>3. 信息共享</h2>
          <p>
            我们不会出售、交易或转让您的个人信息给第三方，除非：
          </p>
          <ul>
            <li>获得您的明确同意</li>
            <li>法律法规要求</li>
            <li>保护我们的权利和安全</li>
            <li>与可信的服务提供商合作（在严格的保密协议下）</li>
          </ul>

          <h2>4. 数据安全</h2>
          <p>
            我们采取多种安全措施保护您的信息：
          </p>
          <ul>
            <li>数据加密传输和存储</li>
            <li>访问控制和身份验证</li>
            <li>定期安全审计和漏洞扫描</li>
            <li>员工安全培训和保密协议</li>
            <li>备份和灾难恢复机制</li>
          </ul>

          <h2>5. 数据保留</h2>
          <p>
            我们仅在必要期间保留您的信息：
          </p>
          <ul>
            <li>账户活跃期间及之后的合理期限</li>
            <li>法律法规要求的保留期限</li>
            <li>解决争议和执行协议所需的时间</li>
          </ul>

          <h2>6. 您的权利</h2>
          <p>您有权：</p>
          <ul>
            <li>访问和查看您的个人信息</li>
            <li>更正不准确的信息</li>
            <li>删除您的账户和相关数据</li>
            <li>限制或反对某些数据处理</li>
            <li>数据可携带性</li>
          </ul>

          <h2>7. Cookie使用</h2>
          <p>
            我们使用Cookie和类似技术来：
          </p>
          <ul>
            <li>记住您的登录状态</li>
            <li>分析网站使用情况</li>
            <li>个性化用户体验</li>
            <li>提供安全保护</li>
          </ul>

          <h2>8. 第三方服务</h2>
          <p>
            我们可能集成第三方服务（如AI模型提供商），这些服务有自己的隐私政策。我们建议您阅读相关政策。
          </p>

          <h2>9. 儿童隐私</h2>
          <p>
            我们的服务不面向13岁以下儿童。如果我们发现收集了儿童信息，将立即删除。
          </p>

          <h2>10. 政策更新</h2>
          <p>
            我们可能会更新本隐私政策。重大变更将通过邮件或网站通知您。
          </p>

          <h2>11. 联系我们</h2>
          <p>
            如果您对本隐私政策有任何疑问，请联系我们：
          </p>
          <ul>
            <li>邮箱：<EMAIL></li>
            <li>地址：[公司地址]</li>
            <li>电话：[联系电话]</li>
          </ul>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="mt-8 text-center">
        <button 
          @click="goBack"
          class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-indigo-600 hover:bg-indigo-700 transition-colors"
        >
          我已阅读并理解
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  router.back()
}
</script>

<style scoped>
.prose h2 {
  @apply text-2xl font-bold text-gray-900 mt-8 mb-4;
}

.prose p {
  @apply text-gray-700 mb-4 leading-relaxed;
}

.prose ul {
  @apply list-disc list-inside text-gray-700 mb-4 space-y-2;
}

.prose li {
  @apply leading-relaxed;
}

.prose strong {
  @apply font-semibold text-gray-900;
}
</style>

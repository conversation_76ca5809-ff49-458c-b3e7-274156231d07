<template>
  <div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 返回按钮 -->
      <div class="mb-8">
        <button 
          @click="goBack"
          class="inline-flex items-center text-indigo-600 hover:text-indigo-500 transition-colors"
        >
          <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          返回
        </button>
      </div>

      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-gray-900 mb-4">服务条款</h1>
        <p class="text-lg text-gray-600">最后更新：2024年12月19日</p>
      </div>

      <!-- 内容区域 -->
      <div class="bg-white rounded-xl shadow-lg p-8 lg:p-12">
        <div class="prose prose-lg max-w-none">
          <h2>1. 服务简介</h2>
          <p>
            欢迎使用Dify AI应用开发平台（以下简称"本服务"）。本服务由Dify团队提供，旨在为用户提供便捷的AI应用开发、部署和管理解决方案。
          </p>

          <h2>2. 服务内容</h2>
          <p>本服务主要包括但不限于：</p>
          <ul>
            <li>可视化工作流构建器</li>
            <li>多种AI模型支持和集成</li>
            <li>知识库管理和集成</li>
            <li>应用部署和监控</li>
            <li>用户管理和权限控制</li>
          </ul>

          <h2>3. 用户责任</h2>
          <p>使用本服务时，您需要：</p>
          <ul>
            <li>提供真实、准确的注册信息</li>
            <li>妥善保管您的账户信息和密码</li>
            <li>遵守相关法律法规和平台规则</li>
            <li>不得利用本服务从事违法违规活动</li>
            <li>尊重他人的知识产权和合法权益</li>
          </ul>

          <h2>4. 服务限制</h2>
          <p>为保障服务质量和安全，我们可能对以下方面进行限制：</p>
          <ul>
            <li>API调用频率和数量</li>
            <li>存储空间和带宽使用</li>
            <li>并发连接数</li>
            <li>特定功能的使用权限</li>
          </ul>

          <h2>5. 知识产权</h2>
          <p>
            本服务的所有内容，包括但不限于软件、技术、设计、文档等，均受知识产权法保护。未经授权，不得复制、修改、分发或用于商业用途。
          </p>

          <h2>6. 隐私保护</h2>
          <p>
            我们重视您的隐私保护，具体的数据收集、使用和保护措施请参阅我们的《隐私政策》。
          </p>

          <h2>7. 服务变更</h2>
          <p>
            我们保留随时修改、暂停或终止本服务的权利。重大变更将提前通知用户。
          </p>

          <h2>8. 免责声明</h2>
          <p>
            本服务按"现状"提供，我们不对服务的连续性、准确性、可靠性做出保证。用户使用本服务的风险由其自行承担。
          </p>

          <h2>9. 争议解决</h2>
          <p>
            因使用本服务产生的争议，双方应友好协商解决。协商不成的，可向有管辖权的人民法院提起诉讼。
          </p>

          <h2>10. 联系我们</h2>
          <p>
            如果您对本服务条款有任何疑问，请通过以下方式联系我们：
          </p>
          <ul>
            <li>邮箱：<EMAIL></li>
            <li>官网：https://dify.ai</li>
          </ul>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="mt-8 text-center">
        <button 
          @click="goBack"
          class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-indigo-600 hover:bg-indigo-700 transition-colors"
        >
          我已阅读并同意
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  router.back()
}
</script>

<style scoped>
.prose h2 {
  @apply text-2xl font-bold text-gray-900 mt-8 mb-4;
}

.prose p {
  @apply text-gray-700 mb-4 leading-relaxed;
}

.prose ul {
  @apply list-disc list-inside text-gray-700 mb-4 space-y-2;
}

.prose li {
  @apply leading-relaxed;
}
</style>

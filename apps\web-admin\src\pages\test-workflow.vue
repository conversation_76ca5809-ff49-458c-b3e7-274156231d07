<template>
  <div class="test-workflow h-screen">
    <h1 class="text-2xl font-bold p-4">Vue Flow 测试页面</h1>
    
    <div class="h-96 border border-gray-300 m-4">
      <VueFlow
        v-model:nodes="nodes"
        v-model:edges="edges"
        :fit-view-on-init="true"
        class="basic-flow"
      >
        <Background 
          pattern-color="#e5e7eb"
          :gap="20"
          variant="dots"
        />
        
        <Controls />
        
        <MiniMap />
      </VueFlow>
    </div>
    
    <div class="p-4">
      <p>节点数量: {{ nodes.length }}</p>
      <p>边数量: {{ edges.length }}</p>
      <button 
        @click="addNode"
        class="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        添加节点
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { VueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'

// 初始节点
const nodes = ref([
  {
    id: '1',
    type: 'default',
    position: { x: 100, y: 100 },
    data: { label: '开始节点' }
  },
  {
    id: '2',
    type: 'default',
    position: { x: 300, y: 100 },
    data: { label: '结束节点' }
  }
])

// 初始边
const edges = ref([
  {
    id: 'e1-2',
    source: '1',
    target: '2'
  }
])

// 添加节点
const addNode = () => {
  const newId = (nodes.value.length + 1).toString()
  nodes.value.push({
    id: newId,
    type: 'default',
    position: { x: Math.random() * 400, y: Math.random() * 300 },
    data: { label: `节点 ${newId}` }
  })
}
</script>

<style scoped>
.basic-flow {
  background-color: #f9fafb;
}

/* Vue Flow 样式 */
:deep(.vue-flow__node) {
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 10px;
  font-size: 12px;
}

:deep(.vue-flow__node.selected) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

:deep(.vue-flow__edge-path) {
  stroke: #6b7280;
  stroke-width: 2;
}

:deep(.vue-flow__edge.selected .vue-flow__edge-path) {
  stroke: #3b82f6;
  stroke-width: 3;
}
</style>

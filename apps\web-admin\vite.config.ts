import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'

export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        'pinia',
        '@vueuse/core',
        'vue-i18n'
      ],
      dts: true,
      eslintrc: {
        enabled: true
      }
    }),
    Components({
      dts: true,
      resolvers: []
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@dify/components': resolve(__dirname, '../../packages/components/src'),
      '@dify/utils': resolve(__dirname, '../../packages/utils/src'),
      '@dify/types': resolve(__dirname, '../../packages/types/src'),
      '@dify/api': resolve(__dirname, '../../packages/api/src'),
      '@dify/constants': resolve(__dirname, '../../packages/constants/src')
    }
  },
  server: {
    port: 5173,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:5001',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '/v1')
      }
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          'ui-vendor': ['@headlessui/vue', '@heroicons/vue'],
          'flow-vendor': ['@vue-flow/core', '@vue-flow/controls', '@vue-flow/minimap'],
          'utils-vendor': ['dayjs', 'lodash-es', 'ky']
        }
      }
    },
    target: 'esnext',
    minify: 'esbuild'
  },
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      '@vueuse/core',
      '@headlessui/vue',
      '@heroicons/vue',
      'dayjs',
      'lodash-es'
    ]
  }
})

<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- 全局加载指示器 -->
    <Suspense>
      <template #default>
        <RouterView />
      </template>
      <template #fallback>
        <div class="flex items-center justify-center min-h-screen">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </template>
    </Suspense>
    
    <!-- 全局通知容器 -->
    <div id="notifications" class="fixed top-4 right-4 z-50"></div>
    
    <!-- 全局模态框容器 -->
    <div id="modals"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { RouterView } from 'vue-router'

// 全局键盘快捷键
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + K 打开搜索
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    // 触发全局搜索
    console.log('Global search triggered')
  }
  
  // ESC 关闭模态框
  if (event.key === 'Escape') {
    // 触发关闭模态框事件
    console.log('ESC pressed')
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  
  // 设置文档标题
  document.title = 'Dify Admin - AI Application Development Platform'
  
  // 添加全局CSS变量
  document.documentElement.style.setProperty('--primary-color', '#3b82f6')
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style>
/* 全局样式重置和基础样式在 styles/index.css 中定义 */
</style>

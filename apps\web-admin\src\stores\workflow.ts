/**
 * 工作流状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type {
  WorkflowConfig,
  WorkflowNode,
  WorkflowEdge,
  WorkflowRun,
  WorkflowRunDetail,
  NodeType,
  NodeData
} from '@/types/workflow'

export const useWorkflowStore = defineStore('workflow', () => {
  // 状态
  const workflows = ref<WorkflowConfig[]>([])
  const currentWorkflow = ref<WorkflowConfig | null>(null)
  const workflowRuns = ref<WorkflowRun[]>([])
  const currentRun = ref<WorkflowRunDetail | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 编辑器状态
  const nodes = ref<WorkflowNode[]>([])
  const edges = ref<WorkflowEdge[]>([])
  const selectedNodes = ref<string[]>([])
  const selectedEdges = ref<string[]>([])
  const viewport = ref({ x: 0, y: 0, zoom: 1 })
  const isExecuting = ref(false)
  const executionLogs = ref<any[]>([])
  const hasUnsavedChanges = ref(false)

  // 计算属性
  const workflowCount = computed(() => workflows.value.length)
  const hasSelectedElements = computed(() =>
    selectedNodes.value.length > 0 || selectedEdges.value.length > 0
  )
  const canExecute = computed(() =>
    nodes.value.length > 0 && !isExecuting.value
  )

  // 工作流管理方法
  const fetchWorkflows = async () => {
    loading.value = true
    error.value = null
    try {
      // TODO: 调用API获取工作流列表
      // const response = await api.workflow.list()
      // workflows.value = response

      // 模拟数据
      workflows.value = [
        {
          id: '1',
          name: '客服聊天机器人',
          description: '智能客服工作流，支持多轮对话和知识库检索',
          mode: 'chatflow',
          graph: {
            nodes: [],
            edges: [],
            viewport: { x: 0, y: 0, zoom: 1 }
          },
          variables: [],
          created_by: 'user1',
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          id: '2',
          name: '文档翻译工作流',
          description: '自动化文档翻译处理流程',
          mode: 'workflow',
          graph: {
            nodes: [],
            edges: [],
            viewport: { x: 0, y: 0, zoom: 1 }
          },
          variables: [],
          created_by: 'user1',
          created_at: '2024-01-02T00:00:00Z'
        }
      ]
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取工作流列表失败'
    } finally {
      loading.value = false
    }
  }

  const createWorkflow = async (data: Partial<WorkflowConfig>) => {
    loading.value = true
    error.value = null
    try {
      // TODO: 调用API创建工作流
      // const response = await api.workflow.create(data)

      // 模拟创建
      const newWorkflow: WorkflowConfig = {
        id: Date.now().toString(),
        name: data.name || '新工作流',
        description: data.description || '',
        mode: data.mode || 'workflow',
        graph: {
          nodes: [],
          edges: [],
          viewport: { x: 0, y: 0, zoom: 1 }
        },
        variables: [],
        created_by: 'current_user',
        created_at: new Date().toISOString()
      }

      workflows.value.unshift(newWorkflow)
      return newWorkflow
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建工作流失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateWorkflow = async (id: string, data: Partial<WorkflowConfig>) => {
    loading.value = true
    error.value = null
    try {
      // TODO: 调用API更新工作流
      // await api.workflow.update(id, data)

      const index = workflows.value.findIndex(w => w.id === id)
      if (index !== -1) {
        workflows.value[index] = { ...workflows.value[index], ...data }
        if (currentWorkflow.value?.id === id) {
          currentWorkflow.value = workflows.value[index]
        }
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新工作流失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteWorkflow = async (id: string) => {
    loading.value = true
    error.value = null
    try {
      // TODO: 调用API删除工作流
      // await api.workflow.delete(id)

      workflows.value = workflows.value.filter(w => w.id !== id)
      if (currentWorkflow.value?.id === id) {
        currentWorkflow.value = null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除工作流失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const loadWorkflow = async (id: string) => {
    loading.value = true
    error.value = null
    try {
      // TODO: 调用API获取工作流详情
      // const response = await api.workflow.get(id)

      const workflow = workflows.value.find(w => w.id === id)
      if (workflow) {
        currentWorkflow.value = workflow
        nodes.value = workflow.graph.nodes || []
        edges.value = workflow.graph.edges || []
        viewport.value = workflow.graph.viewport || { x: 0, y: 0, zoom: 1 }
      } else {
        // 如果没有找到工作流，初始化空的编辑器
        currentWorkflow.value = null
        nodes.value = []
        edges.value = []
        viewport.value = { x: 0, y: 0, zoom: 1 }
      }

      console.log('加载工作流完成，节点数量:', nodes.value.length)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载工作流失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  // 编辑器方法
  const addNode = (node: WorkflowNode) => {
    nodes.value.push(node)
    hasUnsavedChanges.value = true
    saveWorkflowGraph()
  }

  const updateNode = (nodeId: string, data: Partial<NodeData>) => {
    const index = nodes.value.findIndex(n => n.id === nodeId)
    if (index !== -1) {
      nodes.value[index].data = { ...nodes.value[index].data, ...data }
      hasUnsavedChanges.value = true
      saveWorkflowGraph()
    }
  }

  const deleteNode = (nodeId: string) => {
    nodes.value = nodes.value.filter(n => n.id !== nodeId)
    edges.value = edges.value.filter(e => e.source !== nodeId && e.target !== nodeId)
    selectedNodes.value = selectedNodes.value.filter(id => id !== nodeId)
    hasUnsavedChanges.value = true
    saveWorkflowGraph()
  }

  const addEdge = (edge: WorkflowEdge) => {
    edges.value.push(edge)
    hasUnsavedChanges.value = true
    saveWorkflowGraph()
  }

  const deleteEdge = (edgeId: string) => {
    edges.value = edges.value.filter(e => e.id !== edgeId)
    selectedEdges.value = selectedEdges.value.filter(id => id !== edgeId)
    saveWorkflowGraph()
  }

  const selectNode = (nodeId: string, multiple = false) => {
    if (multiple) {
      if (selectedNodes.value.includes(nodeId)) {
        selectedNodes.value = selectedNodes.value.filter(id => id !== nodeId)
      } else {
        selectedNodes.value.push(nodeId)
      }
    } else {
      selectedNodes.value = [nodeId]
    }
    selectedEdges.value = []
  }

  const selectEdge = (edgeId: string, multiple = false) => {
    if (multiple) {
      if (selectedEdges.value.includes(edgeId)) {
        selectedEdges.value = selectedEdges.value.filter(id => id !== edgeId)
      } else {
        selectedEdges.value.push(edgeId)
      }
    } else {
      selectedEdges.value = [edgeId]
    }
    selectedNodes.value = []
  }

  const clearSelection = () => {
    selectedNodes.value = []
    selectedEdges.value = []
  }

  const updateViewport = (newViewport: { x: number; y: number; zoom: number }) => {
    viewport.value = newViewport
    saveWorkflowGraph()
  }

  const saveWorkflowGraph = async () => {
    if (!currentWorkflow.value) return

    const updatedGraph = {
      nodes: nodes.value,
      edges: edges.value,
      viewport: viewport.value
    }

    await updateWorkflow(currentWorkflow.value.id, {
      graph: updatedGraph,
      updated_at: new Date().toISOString()
    })

    hasUnsavedChanges.value = false
  }

  // 工作流执行
  const executeWorkflow = async (inputs: Record<string, any> = {}) => {
    if (!currentWorkflow.value) return

    isExecuting.value = true
    executionLogs.value = []

    try {
      // TODO: 调用API执行工作流
      // const response = await api.workflow.run(currentWorkflow.value.id, inputs)

      // 模拟执行
      await new Promise(resolve => setTimeout(resolve, 2000))

      executionLogs.value.push({
        timestamp: new Date().toISOString(),
        level: 'info',
        message: '工作流执行完成'
      })
    } catch (err) {
      error.value = err instanceof Error ? err.message : '工作流执行失败'
      executionLogs.value.push({
        timestamp: new Date().toISOString(),
        level: 'error',
        message: error.value
      })
    } finally {
      isExecuting.value = false
    }
  }

  const stopExecution = async () => {
    // TODO: 调用API停止执行
    isExecuting.value = false
  }

  // 重置状态
  const reset = () => {
    workflows.value = []
    currentWorkflow.value = null
    workflowRuns.value = []
    currentRun.value = null
    nodes.value = []
    edges.value = []
    selectedNodes.value = []
    selectedEdges.value = []
    viewport.value = { x: 0, y: 0, zoom: 1 }
    isExecuting.value = false
    executionLogs.value = []
    loading.value = false
    error.value = null
  }

  return {
    // 只读状态
    workflows: readonly(workflows),
    currentWorkflow: readonly(currentWorkflow),
    workflowRuns: readonly(workflowRuns),
    currentRun: readonly(currentRun),
    loading: readonly(loading),
    error: readonly(error),

    // 编辑器只读状态
    nodes: readonly(nodes),
    edges: readonly(edges),
    selectedNodes: readonly(selectedNodes),
    selectedEdges: readonly(selectedEdges),
    viewport: readonly(viewport),
    isExecuting: readonly(isExecuting),
    executionLogs: readonly(executionLogs),
    hasUnsavedChanges: readonly(hasUnsavedChanges),

    // 计算属性
    workflowCount,
    hasSelectedElements,
    canExecute,

    // 方法
    fetchWorkflows,
    createWorkflow,
    updateWorkflow,
    deleteWorkflow,
    loadWorkflow,
    addNode,
    updateNode,
    deleteNode,
    addEdge,
    deleteEdge,
    selectNode,
    selectEdge,
    clearSelection,
    updateViewport,
    saveWorkflowGraph,
    executeWorkflow,
    stopExecution,
    reset
  }
})

export type WorkflowStore = ReturnType<typeof useWorkflowStore>

<template>
  <div class="apps-page">
    <!-- 页面头部 -->
    <div class="flex items-center justify-between mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">
          应用程序
        </h1>
        <p class="mt-2 text-base text-gray-600">
          创建和管理您的AI应用程序
        </p>
      </div>

      <button
        class="inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
        @click="showCreateModal = true"
      >
        <PlusIcon class="w-5 h-5 mr-2" />
        创建应用
      </button>
    </div>

    <!-- 筛选和搜索 -->
    <div class="mb-8 flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
      <div class="flex-1 w-full sm:max-w-md">
        <div class="relative">
          <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索应用程序..."
            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
          />
        </div>
      </div>

      <select
        v-model="selectedCategory"
        class="w-full sm:w-48 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
      >
        <option value="all">所有类别</option>
        <option value="chatbot">聊天机器人</option>
        <option value="agent">智能代理</option>
        <option value="workflow">工作流</option>
        <option value="text-generation">文本生成</option>
      </select>
    </div>

    <!-- 应用列表 -->
    <div v-if="isLoading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="i in 6" :key="i" class="animate-pulse">
        <div class="card">
          <div class="card-body">
            <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    </div>

    <div v-else-if="apps.length === 0" class="text-center py-16">
      <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
        <RectangleStackIcon class="h-12 w-12 text-gray-400" />
      </div>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">暂无应用程序</h3>
      <p class="text-gray-500 mb-8 max-w-md mx-auto">
        开始创建您的第一个AI应用程序，体验智能化的工作流程。
      </p>
      <button
        @click="showCreateModal = true"
        class="inline-flex items-center px-6 py-3 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 mx-auto"
      >
        <PlusIcon class="w-5 h-5 mr-2" />
        创建应用
      </button>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
      <div
        v-for="app in filteredApps"
        :key="app.id"
        class="card hover:shadow-lg transition-all duration-200 group"
      >
        <div class="card-body">
          <div class="flex items-start justify-between">
            <RouterLink
              :to="`/apps/${app.id}`"
              class="flex-1 cursor-pointer"
            >
              <h3 class="text-lg font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors duration-200">
                {{ app.name }}
              </h3>
              <p class="text-sm text-gray-600 mb-4 line-clamp-2">
                {{ app.description || '暂无描述' }}
              </p>

              <div class="flex items-center space-x-4 text-xs">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                  {{ getTypeLabel(app.type) }}
                </span>
                <span class="text-gray-500">{{ formatDate(app.created_at) }}</span>
              </div>
            </RouterLink>

            <div class="flex items-center space-x-1 ml-4">
              <button
                @click="editApp(app)"
                class="p-2 text-gray-400 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors duration-200"
                title="编辑应用"
              >
                <PencilIcon class="h-4 w-4" />
              </button>
              <button
                @click="deleteApp(app)"
                class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200"
                title="删除应用"
              >
                <TrashIcon class="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建应用模态框 -->
    <div v-if="showCreateModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div class="bg-white rounded-xl shadow-2xl p-6 w-full max-w-md animate-scale-in">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold text-gray-900">创建应用程序</h2>
          <button
            @click="showCreateModal = false"
            class="p-1 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors duration-200"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="space-y-4 mb-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">应用名称</label>
            <input
              type="text"
              placeholder="输入应用名称..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">应用类型</label>
            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200">
              <option value="">选择应用类型</option>
              <option value="chatbot">聊天机器人</option>
              <option value="agent">智能代理</option>
              <option value="workflow">工作流</option>
              <option value="text-generation">文本生成</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
            <textarea
              placeholder="简要描述您的应用程序..."
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 resize-none"
            ></textarea>
          </div>
        </div>

        <div class="flex justify-end space-x-3">
          <button
            @click="showCreateModal = false"
            class="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors duration-200"
          >
            取消
          </button>
          <button
            @click="createApp"
            class="px-4 py-2 bg-primary-600 text-white hover:bg-primary-700 rounded-lg transition-colors duration-200"
          >
            创建应用
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  RectangleStackIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/vue/24/outline'

const router = useRouter()

// 模拟数据
const apps = ref([
  {
    id: '1',
    name: '客服机器人',
    description: 'AI驱动的智能客服聊天机器人，提供24/7客户支持服务',
    type: 'chatbot',
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name: '内容生成器',
    description: '自动生成营销内容，提高内容创作效率',
    type: 'text-generation',
    created_at: '2024-01-14T15:30:00Z'
  },
  {
    id: '3',
    name: '数据分析助手',
    description: '智能数据分析和洞察，帮助做出更好的业务决策',
    type: 'agent',
    created_at: '2024-01-13T09:15:00Z'
  },
  {
    id: '4',
    name: '文档处理工作流',
    description: '自动化文档处理流程，提高办公效率',
    type: 'workflow',
    created_at: '2024-01-12T14:20:00Z'
  },
  {
    id: '5',
    name: '智能翻译助手',
    description: '多语言智能翻译，支持实时对话翻译',
    type: 'text-generation',
    created_at: '2024-01-11T11:45:00Z'
  },
  {
    id: '6',
    name: '销售助理机器人',
    description: '智能销售助理，协助客户咨询和产品推荐',
    type: 'chatbot',
    created_at: '2024-01-10T16:30:00Z'
  }
])

const isLoading = ref(false)
const showCreateModal = ref(false)
const searchQuery = ref('')
const selectedCategory = ref('all')

// 过滤应用
const filteredApps = computed(() => {
  let filtered = apps.value

  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(app => app.type === selectedCategory.value)
  }

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(app =>
      app.name.toLowerCase().includes(query) ||
      app.description?.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 获取类型标签
const getTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'chatbot': '聊天机器人',
    'agent': '智能代理',
    'workflow': '工作流',
    'text-generation': '文本生成'
  }
  return typeMap[type] || type
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 创建应用
const createApp = () => {
  // 这里应该调用API创建应用
  console.log('创建应用')
  showCreateModal.value = false
  // 临时添加一个新应用到列表
  const newApp = {
    id: String(Date.now()),
    name: '新应用',
    description: '这是一个新创建的应用',
    type: 'chatbot',
    created_at: new Date().toISOString()
  }
  apps.value.unshift(newApp)
}

// 编辑应用
const editApp = (app: any) => {
  console.log('编辑应用:', app)
  // 跳转到编辑页面
  router.push(`/apps/${app.id}/edit`)
}

// 删除应用
const deleteApp = (app: any) => {
  if (confirm(`确定要删除应用"${app.name}"吗？此操作无法撤销。`)) {
    const index = apps.value.findIndex(a => a.id === app.id)
    if (index > -1) {
      apps.value.splice(index, 1)
    }
  }
}
</script>

<template>
  <div
    :class="[
      'workflow-node',
      'relative',
      'bg-white',
      'border-2',
      'rounded-lg',
      'shadow-sm',
      'cursor-pointer',
      'transition-all',
      'duration-200',
      {
        'border-blue-500 shadow-md': selected,
        'border-gray-200 hover:border-gray-300': !selected,
        'opacity-50': disabled
      }
    ]"
    @click="handleClick"
    @dblclick="handleDoubleClick"
    @mousedown="handleMouseDown"
  >
    <!-- 节点头部 -->
    <div
      :class="[
        'flex',
        'items-center',
        'px-3',
        'py-2',
        'rounded-t-lg',
        'border-b',
        'border-gray-100',
        nodeTypeClass
      ]"
    >
      <!-- 节点图标 -->
      <div class="flex-shrink-0 mr-2">
        <component
          :is="nodeIcon"
          class="w-4 h-4 text-white"
        />
      </div>

      <!-- 节点标题 -->
      <div class="flex-1 min-w-0">
        <h3 class="text-sm font-medium text-white truncate">
          {{ data.title }}
        </h3>
      </div>

      <!-- 节点状态指示器 -->
      <div v-if="status" class="flex-shrink-0 ml-2">
        <div
          :class="[
            'w-2',
            'h-2',
            'rounded-full',
            statusClass
          ]"
        />
      </div>
    </div>

    <!-- 节点内容 -->
    <div class="px-3 py-2 min-h-[60px] flex flex-col justify-center">
      <slot name="content">
        <p v-if="data.desc" class="text-xs text-gray-500 line-clamp-2">
          {{ data.desc }}
        </p>
      </slot>
    </div>

    <!-- 输入连接点 -->
    <Handle
      v-if="!isStartNode"
      type="target"
      position="left"
      :style="{
        left: '-8px',
        top: '50%',
        transform: 'translateY(-50%)',
        zIndex: 1000
      }"
      class="workflow-handle workflow-handle-input"
    />

    <!-- 输出连接点 -->
    <Handle
      v-if="!isEndNode"
      type="source"
      position="right"
      :style="{
        right: '-8px',
        top: '50%',
        transform: 'translateY(-50%)',
        zIndex: 1000
      }"
      class="workflow-handle workflow-handle-output"
    />

    <!-- 多输出连接点 -->
    <template v-if="multipleOutputs">
      <Handle
        v-for="(output, index) in outputs"
        :key="output.id"
        :id="output.id"
        type="source"
        position="right"
        :style="{
          right: '-6px',
          top: `${30 + index * 20}px`
        }"
        class="workflow-handle workflow-handle-output"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Handle } from '@vue-flow/core'
import {
  PlayIcon,
  StopIcon,
  CpuChipIcon,
  MagnifyingGlassIcon,
  QuestionMarkCircleIcon,
  CodeBracketIcon,
  DocumentTextIcon,
  GlobeAltIcon,
  WrenchScrewdriverIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/vue/24/outline'
import type { NodeData, NodeType } from '@/types/workflow'

interface Props {
  data: NodeData
  selected?: boolean
  disabled?: boolean
  status?: 'running' | 'success' | 'error' | 'idle'
  multipleOutputs?: boolean
  outputs?: Array<{ id: string; label: string }>
}

interface Emits {
  (e: 'click', event: MouseEvent): void
  (e: 'doubleClick', event: MouseEvent): void
  (e: 'mouseDown', event: MouseEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  disabled: false,
  multipleOutputs: false,
  outputs: () => []
})

const emit = defineEmits<Emits>()

// 节点类型判断
const isStartNode = computed(() => props.data.type === 'start')
const isEndNode = computed(() => props.data.type === 'end')

// 节点图标映射
const nodeIconMap = {
  start: PlayIcon,
  end: StopIcon,
  llm: CpuChipIcon,
  'knowledge-retrieval': MagnifyingGlassIcon,
  'question-classifier': QuestionMarkCircleIcon,
  'if-else': QuestionMarkCircleIcon,
  code: CodeBracketIcon,
  template: DocumentTextIcon,
  'http-request': GlobeAltIcon,
  tools: WrenchScrewdriverIcon,
  answer: ChatBubbleLeftRightIcon
}

const nodeIcon = computed(() => nodeIconMap[props.data.type] || CpuChipIcon)

// 节点类型样式
const nodeTypeClassMap = {
  start: 'bg-green-500',
  end: 'bg-red-500',
  llm: 'bg-blue-500',
  'knowledge-retrieval': 'bg-purple-500',
  'question-classifier': 'bg-yellow-500',
  'if-else': 'bg-orange-500',
  code: 'bg-gray-700',
  template: 'bg-indigo-500',
  'http-request': 'bg-teal-500',
  tools: 'bg-pink-500',
  answer: 'bg-cyan-500'
}

const nodeTypeClass = computed(() => nodeTypeClassMap[props.data.type] || 'bg-gray-500')

// 状态样式
const statusClassMap = {
  running: 'bg-yellow-400 animate-pulse',
  success: 'bg-green-400',
  error: 'bg-red-400',
  idle: 'bg-gray-300'
}

const statusClass = computed(() =>
  props.status ? statusClassMap[props.status] : ''
)

// 事件处理
const handleClick = (event: MouseEvent) => {
  // 检查是否点击的是Handle
  const target = event.target as HTMLElement
  if (target.classList.contains('vue-flow__handle') || target.closest('.vue-flow__handle')) {
    return // 如果点击的是Handle，不处理节点点击事件
  }
  emit('click', event)
}

const handleDoubleClick = (event: MouseEvent) => {
  emit('doubleClick', event)
}

const handleMouseDown = (event: MouseEvent) => {
  // 检查是否点击的是Handle
  const target = event.target as HTMLElement
  if (target.classList.contains('vue-flow__handle') || target.closest('.vue-flow__handle')) {
    return // 如果点击的是Handle，不处理节点mousedown事件
  }
  emit('mouseDown', event)
}
</script>

<style scoped>
.workflow-node {
  min-width: 200px;
  max-width: 280px;
  width: auto;
  box-sizing: border-box;
}

.workflow-handle {
  @apply w-4 h-4 border-2 border-white rounded-full;
  position: absolute;
  background: #6b7280;
  transition: background-color 0.2s ease, transform 0.1s ease;
  cursor: crosshair;
  z-index: 1000;
  pointer-events: auto;
}

.workflow-handle:hover {
  transform: scale(1.1);
  background: #3b82f6;
}

.workflow-handle-input {
  background: #3b82f6;
}

.workflow-handle-input:hover {
  background: #2563eb;
}

.workflow-handle-output {
  background: #10b981;
}

.workflow-handle-output:hover {
  background: #059669;
}
</style>

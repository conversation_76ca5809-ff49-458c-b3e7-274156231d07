<template>
  <header class="bg-white shadow-sm border-b border-gray-200">
    <div class="flex items-center justify-between h-16 px-6">
      <!-- 左侧：移动端菜单按钮 -->
      <div class="flex items-center lg:hidden">
        <button
          @click="$emit('toggleSidebar')"
          class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
        >
          <Bars3Icon class="h-6 w-6" />
        </button>
      </div>

      <!-- 中间：搜索框 -->
      <div class="flex-1 max-w-2xl mx-4 lg:mx-8">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="搜索应用程序、工作流、数据集..."
            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          />
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
            <kbd class="inline-flex items-center border border-gray-200 rounded px-2 py-1 text-xs font-sans font-medium text-gray-400">
              ⌘K
            </kbd>
          </div>
        </div>
      </div>

      <!-- 右侧：通知和用户菜单 -->
      <div class="flex items-center space-x-4">
        <!-- 通知按钮 -->
        <button class="p-2 text-gray-400 hover:text-gray-500 relative">
          <BellIcon class="h-6 w-6" />
          <span class="absolute top-1 right-1 block h-2 w-2 rounded-full bg-red-400"></span>
        </button>

        <!-- 帮助按钮 -->
        <button class="p-2 text-gray-400 hover:text-gray-500">
          <QuestionMarkCircleIcon class="h-6 w-6" />
        </button>

        <!-- 用户菜单 -->
        <div class="relative">
          <button
            @click="showUserMenu = !showUserMenu"
            class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <img
              class="h-8 w-8 rounded-full"
              src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
              alt="User avatar"
            />
          </button>

          <transition
            enter-active-class="transition duration-200 ease-out"
            enter-from-class="transform scale-95 opacity-0"
            enter-to-class="transform scale-100 opacity-100"
            leave-active-class="transition duration-75 ease-in"
            leave-from-class="transform scale-100 opacity-100"
            leave-to-class="transform scale-95 opacity-0"
          >
            <div
              v-if="showUserMenu"
              class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
            >
              <div class="py-1">
                <div class="px-4 py-2 text-sm text-gray-700 border-b border-gray-100">
                  <p class="font-medium">张三</p>
                  <p class="text-gray-500"><EMAIL></p>
                </div>

                <a href="#" @click="showUserMenu = false" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  <UserIcon class="h-4 w-4 mr-2" />
                  个人资料
                </a>

                <a href="#" @click="showUserMenu = false" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  <CogIcon class="h-4 w-4 mr-2" />
                  设置
                </a>

                <a href="#" @click="showUserMenu = false" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  <LifebuoyIcon class="h-4 w-4 mr-2" />
                  帮助支持
                </a>

                <div class="border-t border-gray-100">
                  <a href="#" @click="showUserMenu = false" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    <ArrowRightOnRectangleIcon class="h-4 w-4 mr-2" />
                    退出登录
                  </a>
                </div>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  Bars3Icon,
  MagnifyingGlassIcon,
  BellIcon,
  QuestionMarkCircleIcon,
  UserIcon,
  CogIcon,
  LifebuoyIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/vue/24/outline'

const showUserMenu = ref(false)

defineEmits<{
  toggleSidebar: []
}>()
</script>

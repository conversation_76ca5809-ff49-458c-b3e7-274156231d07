import { test, expect } from '@playwright/test';

test.describe('Edit Button Fix Test', () => {
  test('应该能够点击编辑应用按钮跳转到编辑页', async ({ page }) => {
    // 导航到应用详情页
    await page.goto('/apps/1');
    
    // 等待页面加载完成
    await page.waitForSelector('.app-detail');
    
    // 验证编辑按钮存在且可见
    const editButton = page.locator('text=编辑应用');
    await expect(editButton).toBeVisible();
    
    // 验证按钮有正确的样式类
    await expect(editButton).toHaveClass(/bg-primary-600/);
    await expect(editButton).toHaveClass(/text-white/);
    
    // 点击编辑按钮
    await editButton.click();
    
    // 验证跳转到编辑页面
    await expect(page).toHaveURL('/apps/1/edit');
    
    // 验证编辑页面加载正确
    await expect(page.locator('h1')).toContainText('编辑应用');
    
    // 验证表单字段存在
    await expect(page.locator('input[placeholder="输入应用名称..."]')).toBeVisible();
    await expect(page.locator('select')).toBeVisible();
    await expect(page.locator('textarea')).toBeVisible();
  });

  test('应该能够从编辑页返回到详情页', async ({ page }) => {
    // 直接导航到编辑页
    await page.goto('/apps/1/edit');
    
    // 等待页面加载
    await page.waitForSelector('.app-edit');
    
    // 点击取消按钮
    const cancelButton = page.locator('text=取消');
    await expect(cancelButton).toBeVisible();
    await cancelButton.click();
    
    // 验证返回到详情页
    await expect(page).toHaveURL('/apps/1');
    await expect(page.locator('h1')).toContainText('应用详情');
  });

  test('应该能够从编辑页使用返回链接', async ({ page }) => {
    // 导航到编辑页
    await page.goto('/apps/1/edit');
    
    // 点击返回应用详情链接
    const backLink = page.locator('text=返回应用详情');
    await expect(backLink).toBeVisible();
    await backLink.click();
    
    // 验证返回到详情页
    await expect(page).toHaveURL('/apps/1');
  });

  test('应该验证按钮的悬停效果', async ({ page }) => {
    // 导航到应用详情页
    await page.goto('/apps/1');
    
    // 获取编辑按钮
    const editButton = page.locator('text=编辑应用');
    
    // 悬停在按钮上
    await editButton.hover();
    
    // 验证悬停样式（这里我们检查类名，实际的颜色变化需要更复杂的测试）
    await expect(editButton).toHaveClass(/hover:bg-primary-700/);
  });

  test('应该验证所有按钮都有一致的样式', async ({ page }) => {
    // 测试应用列表页的创建按钮
    await page.goto('/apps');
    const createButton = page.locator('text=创建应用').first();
    await expect(createButton).toHaveClass(/bg-primary-600/);
    await expect(createButton).toHaveClass(/text-white/);
    
    // 测试应用详情页的编辑按钮
    await page.goto('/apps/1');
    const editButton = page.locator('text=编辑应用');
    await expect(editButton).toHaveClass(/bg-primary-600/);
    await expect(editButton).toHaveClass(/text-white/);
    
    // 测试编辑页的保存按钮
    await page.goto('/apps/1/edit');
    const saveButton = page.locator('text=保存更改');
    await expect(saveButton).toHaveClass(/bg-primary-600/);
    await expect(saveButton).toHaveClass(/text-white/);
  });

  test('应该能够保存编辑的应用', async ({ page }) => {
    // 导航到编辑页
    await page.goto('/apps/1/edit');
    
    // 等待表单加载
    await page.waitForSelector('input[placeholder="输入应用名称..."]');
    
    // 修改应用名称
    const nameInput = page.locator('input[placeholder="输入应用名称..."]');
    await nameInput.clear();
    await nameInput.fill('修改后的应用名称');
    
    // 点击保存按钮
    const saveButton = page.locator('text=保存更改');
    
    // 监听alert对话框
    page.on('dialog', dialog => {
      expect(dialog.message()).toContain('应用保存成功');
      dialog.accept();
    });
    
    await saveButton.click();
    
    // 验证返回到详情页
    await expect(page).toHaveURL('/apps/1');
  });
});

<template>
  <div class="app-detail">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center space-x-4 mb-4">
        <RouterLink
          to="/apps"
          class="flex items-center text-gray-500 hover:text-gray-700 transition-colors duration-200"
        >
          <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          返回应用列表
        </RouterLink>
      </div>

      <h1 class="text-3xl font-bold text-gray-900">应用详情</h1>
      <p class="mt-2 text-base text-gray-600">应用ID: {{ $route.params.id }}</p>
    </div>

    <!-- 应用信息卡片 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 主要信息 -->
      <div class="lg:col-span-2">
        <div class="card mb-6">
          <div class="card-header">
            <h2 class="text-lg font-semibold text-gray-900">基本信息</h2>
          </div>
          <div class="card-body">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">应用名称</label>
                <p class="text-gray-900">{{ appData?.name || '加载中...' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">应用描述</label>
                <p class="text-gray-600">{{ appData?.description || '暂无描述' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">应用类型</label>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                  {{ getTypeLabel(appData?.type) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 配置信息 -->
        <div class="card">
          <div class="card-header">
            <h2 class="text-lg font-semibold text-gray-900">配置信息</h2>
          </div>
          <div class="card-body">
            <p class="text-gray-500">配置详情即将推出...</p>
          </div>
        </div>
      </div>

      <!-- 侧边栏信息 -->
      <div class="space-y-6">
        <!-- 状态信息 -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-base font-semibold text-gray-900">状态信息</h3>
          </div>
          <div class="card-body">
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-sm text-gray-500">状态</span>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  运行中
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-500">创建时间</span>
                <span class="text-sm text-gray-900">{{ formatDate(appData?.created_at) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-500">最后更新</span>
                <span class="text-sm text-gray-900">{{ formatDate(appData?.updated_at || appData?.created_at) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-base font-semibold text-gray-900">操作</h3>
          </div>
          <div class="card-body">
            <div class="space-y-3">
              <RouterLink
                :to="`/apps/${$route.params.id}/edit`"
                class="w-full inline-flex items-center justify-center px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                编辑应用
              </RouterLink>
              <button
                @click="deleteApp"
                class="w-full px-4 py-2 text-red-600 bg-red-50 hover:bg-red-100 rounded-lg transition-colors duration-200"
              >
                删除应用
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 应用数据
const appData = ref<any>(null)

// 模拟应用数据
const mockApps = [
  {
    id: '1',
    name: '客服机器人',
    description: 'AI驱动的智能客服聊天机器人，提供24/7客户支持服务',
    type: 'chatbot',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-16T14:30:00Z'
  },
  {
    id: '2',
    name: '内容生成器',
    description: '自动生成营销内容，提高内容创作效率',
    type: 'text-generation',
    created_at: '2024-01-14T15:30:00Z',
    updated_at: '2024-01-15T09:20:00Z'
  },
  {
    id: '3',
    name: '数据分析助手',
    description: '智能数据分析和洞察，帮助做出更好的业务决策',
    type: 'agent',
    created_at: '2024-01-13T09:15:00Z',
    updated_at: '2024-01-14T11:45:00Z'
  }
]

// 获取类型标签
const getTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'chatbot': '聊天机器人',
    'agent': '智能代理',
    'workflow': '工作流',
    'text-generation': '文本生成'
  }
  return typeMap[type] || type
}

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 跳转到编辑页面
const goToEdit = () => {
  const editPath = `/apps/${route.params.id}/edit`
  console.log('尝试跳转到编辑页面:', editPath)
  console.log('当前路由参数:', route.params)
  console.log('当前应用数据:', appData.value)

  // 尝试使用路由名称跳转
  router.push({
    name: 'AppEdit',
    params: { id: route.params.id }
  }).then(() => {
    console.log('路由跳转成功')
  }).catch((error) => {
    console.error('路由跳转失败:', error)
    // 如果命名路由失败，尝试路径跳转
    window.location.href = editPath
  })
}

// 删除应用
const deleteApp = () => {
  if (confirm(`确定要删除应用"${appData.value?.name}"吗？此操作无法撤销。`)) {
    // 这里应该调用API删除应用
    console.log('删除应用:', appData.value?.id)
    // 删除后返回应用列表
    router.push('/apps')
  }
}

// 加载应用数据
const loadAppData = () => {
  const appId = route.params.id as string
  // 模拟API调用
  const app = mockApps.find(app => app.id === appId)
  if (app) {
    appData.value = app
  } else {
    // 应用不存在，返回应用列表
    router.push('/apps')
  }
}

onMounted(() => {
  loadAppData()
})
</script>

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { VueQueryPlugin } from '@tanstack/vue-query'
import { createI18n } from 'vue-i18n'
import App from './App.vue'
import router from './router'

// 样式导入
import './styles/index.css'
import '@vue-flow/core/dist/style.css'
import '@vue-flow/core/dist/theme-default.css'

// 国际化配置
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  fallbackLocale: 'en',
  messages: {
    en: {
      // 英文翻译将在后续添加
    },
    zh: {
      // 中文翻译将在后续添加
    }
  }
})

// Vue Query 配置
const vueQueryOptions = {
  queryClientConfig: {
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5分钟
        cacheTime: 10 * 60 * 1000, // 10分钟
        retry: 2,
        refetchOnWindowFocus: false
      },
      mutations: {
        retry: 1
      }
    }
  }
}

// 创建应用实例
const app = createApp(App)

// 安装插件
app.use(createPinia())
app.use(router)
app.use(i18n)
app.use(VueQueryPlugin, vueQueryOptions)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err, info)
  // 这里可以集成错误监控服务
}

// 挂载应用
app.mount('#app')

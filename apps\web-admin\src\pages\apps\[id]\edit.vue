<template>
  <div class="app-edit">
    <!-- 页面头部 -->
    <div class="mb-8">
      <div class="flex items-center space-x-4 mb-4">
        <RouterLink
          :to="`/apps/${$route.params.id}`"
          class="flex items-center text-gray-500 hover:text-gray-700 transition-colors duration-200"
        >
          <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          返回应用详情
        </RouterLink>
      </div>

      <h1 class="text-3xl font-bold text-gray-900">编辑应用</h1>
      <p class="mt-2 text-base text-gray-600">应用ID: {{ $route.params.id }}</p>
    </div>

    <!-- 编辑表单 -->
    <div class="max-w-4xl">
      <form @submit.prevent="saveApp" class="space-y-6">
        <!-- 基本信息 -->
        <div class="card">
          <div class="card-header">
            <h2 class="text-lg font-semibold text-gray-900">基本信息</h2>
          </div>
          <div class="card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">应用名称 *</label>
                <input
                  v-model="formData.name"
                  type="text"
                  required
                  placeholder="输入应用名称..."
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">应用类型 *</label>
                <select
                  v-model="formData.type"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200"
                >
                  <option value="">选择应用类型</option>
                  <option value="chatbot">聊天机器人</option>
                  <option value="agent">智能代理</option>
                  <option value="workflow">工作流</option>
                  <option value="text-generation">文本生成</option>
                </select>
              </div>

              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">应用描述</label>
                <textarea
                  v-model="formData.description"
                  placeholder="简要描述您的应用程序..."
                  rows="4"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 resize-none"
                ></textarea>
              </div>
            </div>
          </div>
        </div>

        <!-- 配置信息 -->
        <div class="card">
          <div class="card-header">
            <h2 class="text-lg font-semibold text-gray-900">配置信息</h2>
          </div>
          <div class="card-body">
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <p class="text-gray-500">高级配置功能即将推出...</p>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-4">
          <RouterLink
            :to="`/apps/${$route.params.id}`"
            class="inline-flex items-center px-6 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200"
          >
            取消
          </RouterLink>
          <button
            type="submit"
            class="inline-flex items-center px-6 py-2 bg-primary-600 text-white text-sm font-medium hover:bg-primary-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
          >
            保存更改
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 表单数据
const formData = ref({
  name: '',
  type: '',
  description: ''
})

// 模拟应用数据
const mockApps = [
  {
    id: '1',
    name: '客服机器人',
    description: 'AI驱动的智能客服聊天机器人，提供24/7客户支持服务',
    type: 'chatbot',
    created_at: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name: '内容生成器',
    description: '自动生成营销内容，提高内容创作效率',
    type: 'text-generation',
    created_at: '2024-01-14T15:30:00Z'
  },
  {
    id: '3',
    name: '数据分析助手',
    description: '智能数据分析和洞察，帮助做出更好的业务决策',
    type: 'agent',
    created_at: '2024-01-13T09:15:00Z'
  }
]

// 保存应用
const saveApp = () => {
  // 这里应该调用API保存应用
  console.log('保存应用:', formData.value)

  // 模拟保存成功
  alert('应用保存成功！')

  // 返回应用详情页面
  router.push(`/apps/${route.params.id}`)
}

// 加载应用数据
const loadAppData = () => {
  const appId = route.params.id as string
  // 模拟API调用
  const app = mockApps.find(app => app.id === appId)
  if (app) {
    formData.value = {
      name: app.name,
      type: app.type,
      description: app.description
    }
  } else {
    // 应用不存在，返回应用列表
    router.push('/apps')
  }
}

onMounted(() => {
  loadAppData()
})
</script>

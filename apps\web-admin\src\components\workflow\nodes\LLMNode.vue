<template>
  <BaseNode
    :data="data"
    :selected="selected"
    :disabled="disabled"
    :status="status"
    @click="$emit('click', $event)"
    @double-click="$emit('doubleClick', $event)"
  >
    <template #content>
      <div class="space-y-2 w-full">
        <div class="flex items-center justify-between">
          <p class="text-xs font-medium text-gray-700">LLM 节点</p>
          <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded truncate max-w-20">
            {{ data.model?.name || '未配置' }}
          </span>
        </div>

        <div v-if="data.model?.provider" class="text-xs text-gray-600 truncate">
          <span class="font-medium">提供商:</span> {{ data.model.provider }}
        </div>

        <div v-if="data.prompt_template?.length" class="space-y-1">
          <p class="text-xs font-medium text-gray-700">提示词:</p>
          <div class="bg-gray-50 p-2 rounded text-xs max-h-12 overflow-hidden">
            <div class="text-gray-600 line-clamp-2">
              {{ data.prompt_template[0]?.text?.substring(0, 60) || '未配置' }}{{
                (data.prompt_template[0]?.text?.length || 0) > 60 ? '...' : ''
              }}
            </div>
          </div>
        </div>

        <div class="flex flex-wrap gap-1">
          <span v-if="data.memory?.window?.enabled" class="text-xs bg-blue-100 text-blue-700 px-1 py-0.5 rounded">
            记忆
          </span>
          <span v-if="data.context?.enabled" class="text-xs bg-green-100 text-green-700 px-1 py-0.5 rounded">
            上下文
          </span>
          <span v-if="data.vision?.enabled" class="text-xs bg-purple-100 text-purple-700 px-1 py-0.5 rounded">
            视觉
          </span>
        </div>
      </div>
    </template>
  </BaseNode>
</template>

<script setup lang="ts">
import BaseNode from './BaseNode.vue'
import type { LLMNodeData } from '@/types/workflow'

interface Props {
  data: LLMNodeData
  selected?: boolean
  disabled?: boolean
  status?: 'running' | 'success' | 'error' | 'idle'
}

interface Emits {
  (e: 'click', event: MouseEvent): void
  (e: 'doubleClick', event: MouseEvent): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>

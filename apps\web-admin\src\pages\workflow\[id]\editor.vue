<template>
  <div class="workflow-editor-page h-screen flex flex-col">
    <!-- 顶部导航栏 -->
    <div class="flex-shrink-0 bg-white border-b border-gray-200 px-4 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <button
            @click="goBack"
            class="p-2 hover:bg-gray-100 rounded-lg"
          >
            <ArrowLeftIcon class="w-5 h-5 text-gray-600" />
          </button>
          <div>
            <h1 class="text-lg font-semibold text-gray-900">
              {{ currentWorkflow?.name || '工作流编辑器' }}
            </h1>
            <p class="text-sm text-gray-500">
              {{ currentWorkflow?.mode === 'chatflow' ? '聊天流' : '工作流' }}
            </p>
          </div>
        </div>

        <div class="flex items-center space-x-2">
          <span v-if="lastSaved" class="text-xs text-gray-500">
            最后保存: {{ formatTime(lastSaved) }}
          </span>
          <button
            @click="handlePublish"
            class="btn btn-success btn-sm"
          >
            <RocketLaunchIcon class="w-4 h-4 mr-1" />
            发布
          </button>
        </div>
      </div>
    </div>

    <!-- 工作流编辑器 -->
    <div class="flex-1">
      <WorkflowEditor
        :workflow-id="workflowId"
        :workflow-name="currentWorkflow?.name"
        @save="handleSave"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ArrowLeftIcon, RocketLaunchIcon } from '@heroicons/vue/24/outline'
import { useWorkflowStore } from '@/stores/workflow'
import WorkflowEditor from '@/components/workflow/WorkflowEditor.vue'

const route = useRoute()
const router = useRouter()
const workflowStore = useWorkflowStore()

// 响应式数据
const lastSaved = ref<Date | null>(null)

// 计算属性
const workflowId = computed(() => route.params.id as string)
const currentWorkflow = computed(() => workflowStore.currentWorkflow)

// 方法
const goBack = () => {
  router.push('/workflow')
}

const handleSave = () => {
  lastSaved.value = new Date()
}

const handlePublish = async () => {
  try {
    // TODO: 实现发布逻辑
    console.log('发布工作流:', workflowId.value)
  } catch (error) {
    console.error('发布失败:', error)
  }
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 页面离开前确认
const handleBeforeUnload = (event: BeforeUnloadEvent) => {
  // 如果有未保存的更改，提示用户
  if (workflowStore.hasUnsavedChanges) {
    event.preventDefault()
    event.returnValue = '您有未保存的更改，确定要离开吗？'
  }
}

// 生命周期
onMounted(async () => {
  if (workflowId.value) {
    await workflowStore.loadWorkflow(workflowId.value)
  }
  window.addEventListener('beforeunload', handleBeforeUnload)
})

onBeforeUnmount(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload)
})
</script>

<style scoped>
.workflow-editor-page {
  background-color: #f9fafb;
}
</style>

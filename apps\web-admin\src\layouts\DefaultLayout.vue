<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 侧边栏 -->
    <Sidebar
      :is-open="sidebarOpen"
      @close="sidebarOpen = false"
    />

    <!-- 主内容区域 -->
    <div class="lg:pl-64">
      <!-- 顶部导航栏 -->
      <Header
        @toggle-sidebar="sidebarOpen = !sidebarOpen"
      />

      <!-- 面包屑导航 -->
      <Breadcrumb
        v-if="showBreadcrumb"
        :items="breadcrumbItems"
        class="px-6 py-4"
      />

      <!-- 页面内容 -->
      <main class="px-6 pb-8">
        <RouterView />
      </main>
    </div>

    <!-- 移动端遮罩层 -->
    <div
      v-if="sidebarOpen"
      class="fixed inset-0 z-40 lg:hidden"
      @click="sidebarOpen = false"
    >
      <div class="absolute inset-0 bg-gray-600 opacity-75"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import Sidebar from '@/components/layout/Sidebar.vue'
import Header from '@/components/layout/Header.vue'
import Breadcrumb from '@/components/layout/Breadcrumb.vue'

const route = useRoute()
const sidebarOpen = ref(false)

// 面包屑导航
const showBreadcrumb = computed(() => {
  return route.meta.breadcrumb && route.meta.breadcrumb.length > 0
})

const breadcrumbItems = computed(() => {
  return route.meta.breadcrumb || []
})

// 响应式处理
const handleResize = () => {
  if (window.innerWidth >= 1024) {
    sidebarOpen.value = false
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>
